using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text;

public class LuaViewGenerator : EditorWindow
{
    [MenuItem("Assets/Generate Lua View", true)]
    private static bool ValidateGenerateLuaView()
    {
        // 验证选中的是否为prefab文件且在Resources/UI目录下
        if (Selection.activeObject == null) return false;
        
        string assetPath = AssetDatabase.GetAssetPath(Selection.activeObject);
        return assetPath.Contains("Resources/UI") && assetPath.EndsWith(".prefab");
    }

    [MenuItem("Assets/Generate Lua View")]
    private static void GenerateLuaView()
    {
        if (Selection.activeObject == null) return;

        string prefabPath = AssetDatabase.GetAssetPath(Selection.activeObject);
        string prefabName = Path.GetFileNameWithoutExtension(prefabPath);
        
        // 生成Lua代码
        string luaCode = GenerateLuaViewCode(prefabName, prefabPath);
        
        // 保存到Resources/LuaScripts目录
        string luaScriptPath = Path.Combine("Assets/Resources/LuaScripts", prefabName + ".txt");
        
        try
        {
            File.WriteAllText(luaScriptPath, luaCode, Encoding.UTF8);
            AssetDatabase.Refresh();
            
            Debug.Log($"Lua View代码已生成: {luaScriptPath}");
            
            // 选中生成的文件
            Object luaAsset = AssetDatabase.LoadAssetAtPath<Object>(luaScriptPath);
            Selection.activeObject = luaAsset;
            EditorGUIUtility.PingObject(luaAsset);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"生成Lua View代码失败: {e.Message}");
        }
    }

    private static string GenerateLuaViewCode(string prefabName, string prefabPath)
    {
        // 计算相对于Resources目录的路径
        string resourcePath = GetResourcePath(prefabPath);
        
        StringBuilder sb = new StringBuilder();
        
        // 生成Lua代码模板
        sb.AppendLine($"local {prefabName} = {{}}");
        sb.AppendLine();
        sb.AppendLine("-- 存储GameObject引用");
        sb.AppendLine($"{prefabName}.gameObject = nil");
        sb.AppendLine($"{prefabName}.transform = nil");
        sb.AppendLine();
        sb.AppendLine("-- 资源路径");
        sb.AppendLine($"local PREFAB_PATH = \"{resourcePath}\"");
        sb.AppendLine();
        sb.AppendLine("-- 加载prefab并实例化");
        sb.AppendLine($"function {prefabName}:LoadPrefab()");
        sb.AppendLine("    local prefab = CS.UnityEngine.Resources.Load(PREFAB_PATH)");
        sb.AppendLine("    if prefab then");
        sb.AppendLine("        self.gameObject = CS.UnityEngine.Object.Instantiate(prefab)");
        sb.AppendLine("        self.transform = self.gameObject.transform");
        sb.AppendLine($"        print(\"{prefabName}: Prefab loaded successfully\")");
        sb.AppendLine("        return self.gameObject");
        sb.AppendLine("    else");
        sb.AppendLine($"        print(\"{prefabName}: Failed to load prefab from path: \" .. PREFAB_PATH)");
        sb.AppendLine("        return nil");
        sb.AppendLine("    end");
        sb.AppendLine("end");
        sb.AppendLine();
        sb.AppendLine($"function {prefabName}:OnCreate(gameObject)");
        sb.AppendLine($"    print(\"{prefabName}: OnCreate called\")");
        sb.AppendLine();
        sb.AppendLine("    -- 如果没有传入gameObject，则加载prefab");
        sb.AppendLine("    if not gameObject then");
        sb.AppendLine("        gameObject = self:LoadPrefab()");
        sb.AppendLine("    end");
        sb.AppendLine();
        sb.AppendLine("    -- 存储GameObject引用供其他方法使用");
        sb.AppendLine("    self.gameObject = gameObject");
        sb.AppendLine("    if gameObject then");
        sb.AppendLine("        self.transform = gameObject.transform");
        sb.AppendLine($"        print(\"{prefabName}: GameObject name = \" .. gameObject.name)");
        sb.AppendLine("    else");
        sb.AppendLine($"        print(\"{prefabName}: GameObject is nil!\")");
        sb.AppendLine("        return");
        sb.AppendLine("    end");
        sb.AppendLine();
        sb.AppendLine("    -- 在这里获取UI组件引用");
        sb.AppendLine("    self:InitComponents()");
        sb.AppendLine("end");
        sb.AppendLine();
        sb.AppendLine($"function {prefabName}:InitComponents()");
        sb.AppendLine("    -- 获取UI组件引用");
        sb.AppendLine("    -- 例如:");
        sb.AppendLine("    -- self.button = self.transform:Find(\"Button\"):GetComponent(typeof(CS.UnityEngine.UI.Button))");
        sb.AppendLine("    -- self.text = self.transform:Find(\"Text\"):GetComponent(typeof(CS.UnityEngine.UI.Text))");
        sb.AppendLine("    -- self.image = self.transform:Find(\"Image\"):GetComponent(typeof(CS.UnityEngine.UI.Image))");
        sb.AppendLine("end");
        sb.AppendLine();
        sb.AppendLine($"function {prefabName}:OnShow()");
        sb.AppendLine($"    print(\"{prefabName}: OnShow\")");
        sb.AppendLine("    if self.gameObject then");
        sb.AppendLine("        self.gameObject:SetActive(true)");
        sb.AppendLine("    end");
        sb.AppendLine("end");
        sb.AppendLine();
        sb.AppendLine($"function {prefabName}:OnHide()");
        sb.AppendLine($"    print(\"{prefabName}: OnHide\")");
        sb.AppendLine("    if self.gameObject then");
        sb.AppendLine("        self.gameObject:SetActive(false)");
        sb.AppendLine("    end");
        sb.AppendLine("end");
        sb.AppendLine();
        sb.AppendLine($"function {prefabName}:OnDestroy()");
        sb.AppendLine($"    print(\"{prefabName}: OnDestroy\")");
        sb.AppendLine("    if self.gameObject then");
        sb.AppendLine("        CS.UnityEngine.Object.Destroy(self.gameObject)");
        sb.AppendLine("        self.gameObject = nil");
        sb.AppendLine("        self.transform = nil");
        sb.AppendLine("    end");
        sb.AppendLine("end");
        sb.AppendLine();
        sb.AppendLine($"function {prefabName}:OnUpdate()");
        sb.AppendLine("    -- UI更新逻辑");
        sb.AppendLine("end");
        sb.AppendLine();
        sb.AppendLine($"return {prefabName}");
        
        return sb.ToString();
    }

    private static string GetResourcePath(string assetPath)
    {
        // 从Assets/Resources/路径中提取相对路径
        int resourcesIndex = assetPath.IndexOf("Resources/");
        if (resourcesIndex >= 0)
        {
            string relativePath = assetPath.Substring(resourcesIndex + "Resources/".Length);
            // 移除.prefab扩展名
            if (relativePath.EndsWith(".prefab"))
            {
                relativePath = relativePath.Substring(0, relativePath.Length - ".prefab".Length);
            }
            return relativePath;
        }
        return assetPath;
    }
}
