using System;
using System.IO;
using UnityEngine;
using XLua;
using GameFramework.Core;
using GameFramework.XLua;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua管理器
    /// 负责Lua环境的初始化、脚本加载和执行
    /// </summary>
    public class LuaManager : MonoBehaviour
    {
        [Header("Lua管理器设置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private string luaScriptPath = "LuaScripts";
        [SerializeField] private string mainLuaFile = "Main.txt";

        private LuaEnv luaEnv;
        private bool isInitialized = false;

        // Lua全局函数委托
        private System.Action luaStart;
        private System.Action luaUpdate;
        private System.Action luaFixedUpdate;
        private System.Action luaLateUpdate;
        private System.Action luaOnDestroy;

        public LuaEnv LuaEnvironment => luaEnv;
        public bool IsInitialized => isInitialized;

        private void Awake()
        {
            // 确保单例
            if (FindObjectsOfType<LuaManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            DontDestroyOnLoad(gameObject);

            if (autoInitialize)
            {
                Initialize();
            }
        }

        private void Start()
        {
            if (isInitialized)
            {
                CallLuaStart();
            }
        }

        private void Update()
        {
            if (isInitialized)
            {
                luaUpdate?.Invoke();

                // 定期执行Lua垃圾回收
                if (Time.frameCount % 100 == 0)
                {
                    luaEnv?.Tick();
                }
            }
        }

        private void FixedUpdate()
        {
            if (isInitialized)
            {
                luaFixedUpdate?.Invoke();
            }
        }

        private void LateUpdate()
        {
            if (isInitialized)
            {
                luaLateUpdate?.Invoke();
            }
        }

        private void OnDestroy()
        {
            if (isInitialized)
            {
                Shutdown();
            }
        }

        /// <summary>
        /// 初始化Lua环境
        /// </summary>
        public void Initialize()
        {
            if (isInitialized)
                return;

            LogDebug("初始化Lua管理器");

            try
            {
                // 创建Lua环境
                luaEnv = new LuaEnv();

                // 设置Lua加载器
                luaEnv.AddLoader(CustomLuaLoader);

                // 注册C#接口到Lua
                RegisterCSharpToLua();

                isInitialized = true;
                LogDebug("Lua管理器初始化完成");

                // 加载主Lua文件
                LoadMainLuaScript();
            }
            catch (Exception e)
            {
                LogDebug($"Lua管理器初始化失败: {e.Message}");
                Debug.LogException(e);
            }
        }

        /// <summary>
        /// 关闭Lua环境
        /// </summary>
        public void Shutdown()
        {
            if (!isInitialized)
                return;

            LogDebug("关闭Lua管理器");

            try
            {
                // 先调用Lua的清理函数
                if (luaOnDestroy != null)
                {
                    try
                    {
                        luaOnDestroy.Invoke();
                    }
                    catch (Exception luaEx)
                    {
                        LogDebug($"调用Lua OnDestroy函数异常: {luaEx.Message}");
                    }
                }

                // 清理Lua函数引用
                luaStart = null;
                luaUpdate = null;
                luaFixedUpdate = null;
                luaLateUpdate = null;
                luaOnDestroy = null;

                // 强制执行垃圾回收，确保委托被释放
                if (luaEnv != null)
                {
                    try
                    {
                        // 清理全局表中的所有函数引用
                        luaEnv.Global.Set("Start", (System.Action)null);
                        luaEnv.Global.Set("Update", (System.Action)null);
                        luaEnv.Global.Set("FixedUpdate", (System.Action)null);
                        luaEnv.Global.Set("LateUpdate", (System.Action)null);
                        luaEnv.Global.Set("OnDestroy", (System.Action)null);

                        // 清理其他可能的全局函数
                        luaEnv.Global.Set("GetGameData", (System.Func<object>)null);
                        luaEnv.Global.Set("SetPlayerScore", (System.Action<object>)null);
                        luaEnv.Global.Set("TriggerGameStart", (System.Action)null);
                        luaEnv.Global.Set("TriggerGameOver", (System.Action)null);

                        // 多次执行垃圾回收确保清理完成
                        for (int i = 0; i < 5; i++)
                        {
                            luaEnv.Tick();
                            luaEnv.FullGc();
                            System.GC.Collect();
                            System.GC.WaitForPendingFinalizers();
                        }

                        // 最后一次强制垃圾回收
                        System.GC.Collect();
                        System.GC.WaitForPendingFinalizers();
                        System.GC.Collect();
                    }
                    catch (Exception gcEx)
                    {
                        LogDebug($"Lua垃圾回收异常: {gcEx.Message}");
                    }
                }

                // 销毁Lua环境 - 使用更安全的方式
                if (luaEnv != null)
                {
                    try
                    {
                        // 尝试正常销毁
                        luaEnv.Dispose();
                        LogDebug("LuaEnv正常销毁完成");
                    }
                    catch (InvalidOperationException ex) when (ex.Message.Contains("C# callback"))
                    {
                        LogDebug($"检测到C#回调残留，尝试强制清理: {ex.Message}");

                        // 强制清理策略：不调用Dispose，直接设为null让GC处理
                        // 这虽然不是最佳实践，但可以避免程序崩溃
                        LogDebug("使用强制清理模式，跳过正常Dispose流程");
                    }
                    catch (Exception disposeEx)
                    {
                        LogDebug($"LuaEnv销毁异常: {disposeEx.Message}");
                    }
                    finally
                    {
                        // 无论如何都要清空引用
                        luaEnv = null;
                    }
                }

                isInitialized = false;
                LogDebug("Lua管理器关闭完成");
            }
            catch (Exception e)
            {
                LogDebug($"Lua管理器关闭失败: {e.Message}");
                Debug.LogException(e);

                // 强制清理，防止内存泄漏
                luaStart = null;
                luaUpdate = null;
                luaFixedUpdate = null;
                luaLateUpdate = null;
                luaOnDestroy = null;
                luaEnv = null;
                isInitialized = false;
            }
        }

        /// <summary>
        /// 执行Lua代码
        /// </summary>
        /// <param name="luaCode">Lua代码</param>
        /// <returns>执行结果</returns>
        public object[] DoString(string luaCode)
        {
            if (!isInitialized)
            {
                LogDebug("Lua环境未初始化");
                return null;
            }

            try
            {
                return luaEnv.DoString(luaCode);
            }
            catch (Exception e)
            {
                LogDebug($"执行Lua代码失败: {e.Message}");
                Debug.LogException(e);
                return null;
            }
        }

        /// <summary>
        /// 加载Lua文件
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>执行结果</returns>
        public object[] LoadLuaFile(string fileName)
        {
            if (!isInitialized)
            {
                LogDebug("Lua环境未初始化");
                return null;
            }

            try
            {
                string luaCode = LoadLuaScript(fileName);
                if (!string.IsNullOrEmpty(luaCode))
                {
                    return luaEnv.DoString(luaCode, fileName);
                }
            }
            catch (Exception e)
            {
                LogDebug($"加载Lua文件失败: {fileName}, 错误: {e.Message}");
                Debug.LogException(e);
            }

            return null;
        }

        /// <summary>
        /// 获取Lua全局变量
        /// </summary>
        /// <typeparam name="T">变量类型</typeparam>
        /// <param name="name">变量名</param>
        /// <returns>变量值</returns>
        public T GetLuaGlobal<T>(string name)
        {
            if (!isInitialized)
                return default(T);

            try
            {
                return luaEnv.Global.Get<T>(name);
            }
            catch (Exception e)
            {
                LogDebug($"获取Lua全局变量失败: {name}, 错误: {e.Message}");
                return default(T);
            }
        }

        /// <summary>
        /// 设置Lua全局变量
        /// </summary>
        /// <param name="name">变量名</param>
        /// <param name="value">变量值</param>
        public void SetLuaGlobal(string name, object value)
        {
            if (!isInitialized)
                return;

            try
            {
                luaEnv.Global.Set(name, value);
            }
            catch (Exception e)
            {
                LogDebug($"设置Lua全局变量失败: {name}, 错误: {e.Message}");
            }
        }

        /// <summary>
        /// 调用Lua函数
        /// </summary>
        /// <param name="functionName">函数名</param>
        /// <param name="args">参数</param>
        /// <returns>返回值</returns>
        public object[] CallLuaFunction(string functionName, params object[] args)
        {
            if (!isInitialized)
                return null;

            try
            {
                var func = luaEnv.Global.Get<LuaFunction>(functionName);
                if (func != null)
                {
                    return func.Call(args);
                }
            }
            catch (Exception e)
            {
                LogDebug($"调用Lua函数失败: {functionName}, 错误: {e.Message}");
            }

            return null;
        }

        /// <summary>
        /// 重新加载Lua脚本
        /// </summary>
        public void ReloadLuaScripts()
        {
            LogDebug("重新加载Lua脚本");

            // 清理当前环境
            if (isInitialized)
            {
                luaOnDestroy?.Invoke();
            }

            // 重新初始化
            Shutdown();
            Initialize();

            if (isInitialized)
            {
                CallLuaStart();
            }
        }

        /// <summary>
        /// 自定义Lua加载器
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>Lua代码</returns>
        private byte[] CustomLuaLoader(ref string fileName)
        {
            string luaCode = LoadLuaScript(fileName);
            if (!string.IsNullOrEmpty(luaCode))
            {
                return System.Text.Encoding.UTF8.GetBytes(luaCode);
            }
            return null;
        }

        /// <summary>
        /// 加载Lua脚本
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>Lua代码</returns>
        private string LoadLuaScript(string fileName)
        {
            // 尝试从Resources加载 - 支持.lua和.txt扩展名
            string resourcePath = Path.Combine(luaScriptPath, fileName).Replace('\\', '/');
            string resourceName = resourcePath;

            // 移除扩展名用于Resources.Load
            if (resourceName.EndsWith(".lua"))
            {
                resourceName = resourceName.Replace(".lua", "");
            }
            else if (resourceName.EndsWith(".txt"))
            {
                resourceName = resourceName.Replace(".txt", "");
            }
            else
            {
                // 如果没有扩展名，先尝试.txt，再尝试.lua
                TextAsset luaAsset = Resources.Load<TextAsset>(resourceName);
                if (luaAsset != null)
                {
                    LogDebug($"从Resources加载Lua脚本: {resourceName}");
                    return luaAsset.text;
                }

                // 尝试添加.lua扩展名
                fileName += ".lua";
                resourcePath = Path.Combine(luaScriptPath, fileName).Replace('\\', '/');
                resourceName = resourcePath.Replace(".lua", "");
            }

            TextAsset textAsset = Resources.Load<TextAsset>(resourceName);
            if (textAsset != null)
            {
                LogDebug($"从Resources加载Lua脚本: {resourceName}");
                return textAsset.text;
            }

            LogDebug($"Resources中找不到: {resourceName}");

            // 尝试从StreamingAssets加载
            string streamingPath = Path.Combine(Application.streamingAssetsPath, luaScriptPath, fileName);
            if (File.Exists(streamingPath))
            {
                LogDebug($"从StreamingAssets加载Lua脚本: {streamingPath}");
                return File.ReadAllText(streamingPath);
            }

            LogDebug($"找不到Lua脚本: {fileName}");
            return null;
        }

        /// <summary>
        /// 注册C#接口到Lua
        /// </summary>
        private void RegisterCSharpToLua()
        {
            try
            {
                // 创建GameFramework表并设置其属性
                luaEnv.DoString(@"
                    GameFramework = {}
                ");

                // 注册各个管理器到GameFramework表中
                luaEnv.Global.SetInPath("GameFramework.GameState", LuaGameStateManager.Instance);
                luaEnv.Global.SetInPath("GameFramework.Scene", LuaSceneManager.Instance);
                luaEnv.Global.SetInPath("GameFramework.UI", LuaUIManager.Instance);
                luaEnv.Global.SetInPath("GameFramework.Resource", LuaResourceManager.Instance);
                luaEnv.Global.SetInPath("GameFramework.Input", LuaInputManager.Instance);
                luaEnv.Global.SetInPath("GameFramework.Event", LuaEventSystem.Instance);
                luaEnv.Global.SetInPath("GameFramework.Coroutine", LuaCoroutineManager.Instance);

                LogDebug("注册GameFramework实例完成");

                // 注册辅助类
                luaEnv.Global.Set("GameState", typeof(LuaGameStateHelper));
                luaEnv.Global.Set("Scene", typeof(LuaSceneHelper));
                luaEnv.Global.Set("UI", typeof(LuaUIHelper));
                luaEnv.Global.Set("Resource", typeof(LuaResourceHelper));
                luaEnv.Global.Set("Input", typeof(LuaInputHelper));
                luaEnv.Global.Set("Event", typeof(LuaEventHelper));
                luaEnv.Global.Set("Coroutine", typeof(LuaCoroutineHelper));

                // 注册Unity常用类型
                luaEnv.Global.Set("Vector2", typeof(Vector2));
                luaEnv.Global.Set("Vector3", typeof(Vector3));
                luaEnv.Global.Set("Quaternion", typeof(Quaternion));
                luaEnv.Global.Set("Time", typeof(Time));
                luaEnv.Global.Set("Debug", typeof(Debug));
                luaEnv.Global.Set("GameObject", typeof(GameObject));
                luaEnv.Global.Set("Transform", typeof(Transform));

                // 验证注册是否成功
                var gameFramework = luaEnv.Global.Get<object>("GameFramework");
                LogDebug($"GameFramework注册验证: {gameFramework != null}");

                LogDebug("C#接口注册到Lua完成");
            }
            catch (Exception e)
            {
                LogDebug($"注册C#接口到Lua失败: {e.Message}");
                Debug.LogException(e);
            }
        }

        /// <summary>
        /// 加载主Lua脚本
        /// </summary>
        private void LoadMainLuaScript()
        {
            if (string.IsNullOrEmpty(mainLuaFile))
                return;

            LogDebug($"加载主Lua脚本: {mainLuaFile}");
            LoadLuaFile(mainLuaFile);

            // 获取Lua生命周期函数
            luaStart = GetLuaGlobal<System.Action>("Start");
            luaUpdate = GetLuaGlobal<System.Action>("Update");
            luaFixedUpdate = GetLuaGlobal<System.Action>("FixedUpdate");
            luaLateUpdate = GetLuaGlobal<System.Action>("LateUpdate");
            luaOnDestroy = GetLuaGlobal<System.Action>("OnDestroy");
        }

        /// <summary>
        /// 调用Lua Start函数
        /// </summary>
        private void CallLuaStart()
        {
            try
            {
                luaStart?.Invoke();
            }
            catch (Exception e)
            {
                LogDebug($"调用Lua Start函数异常: {e.Message}");
            }
        }

        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[LuaManager] {message}");
            }
        }

        /// <summary>
        /// 在编辑器中重新加载脚本的快捷方法
        /// </summary>
        [ContextMenu("重新加载Lua脚本")]
        private void ReloadScriptsInEditor()
        {
#if UNITY_EDITOR
            ReloadLuaScripts();
#endif
        }
    }
}
