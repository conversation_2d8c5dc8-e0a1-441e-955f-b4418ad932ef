using UnityEngine;

namespace GameFramework.XLua
{
    /// <summary>
    /// 通用Lua UI组件
    /// 用于没有特定UI组件时的默认组件
    /// </summary>
    public class LuaUIComponent : MonoBehaviour
    {
        [Header("UI信息")]
        [SerializeField] private string uiName;
        [SerializeField] private bool isVisible = true;
        
        /// <summary>
        /// UI名称
        /// </summary>
        public string UIName 
        { 
            get => uiName; 
            set => uiName = value; 
        }
        
        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible 
        { 
            get => isVisible; 
            set 
            { 
                isVisible = value;
                gameObject.SetActive(value);
            } 
        }
        
        private void Awake()
        {
            // 初始化UI组件
            if (string.IsNullOrEmpty(uiName))
            {
                uiName = gameObject.name;
            }
        }
        
        private void Start()
        {
            // 设置初始可见性
            gameObject.SetActive(isVisible);
        }
        
        /// <summary>
        /// 显示UI
        /// </summary>
        public void Show()
        {
            IsVisible = true;
        }
        
        /// <summary>
        /// 隐藏UI
        /// </summary>
        public void Hide()
        {
            IsVisible = false;
        }
        
        /// <summary>
        /// 切换可见性
        /// </summary>
        public void Toggle()
        {
            IsVisible = !IsVisible;
        }
    }
}
