using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace GameFramework.XLua
{
    /// <summary>
    /// UI组件收集器
    /// 用于收集prefab中的UI组件，方便Lua脚本访问
    /// </summary>
    public class UIComponentCollector : MonoBehaviour
    {
        [System.Serializable]
        public class ComponentInfo
        {
            [Header("组件信息")]
            public string name;           // 组件名称
            public Component component;   // 组件引用
            public string componentType;  // 组件类型
            public string path;          // 组件在层级中的路径
            
            public ComponentInfo(string name, Component component, string path)
            {
                this.name = name;
                this.component = component;
                this.componentType = component.GetType().Name;
                this.path = path;
            }
        }
        
        [Header("收集的组件列表")]
        [SerializeField] private List<ComponentInfo> collectedComponents = new List<ComponentInfo>();
        
        /// <summary>
        /// 获取收集的组件列表
        /// </summary>
        public List<ComponentInfo> CollectedComponents => collectedComponents;
        
        /// <summary>
        /// 添加组件到收集列表
        /// </summary>
        public void AddComponent(string name, Component component, string path)
        {
            // 检查是否已存在
            var existing = collectedComponents.Find(c => c.component == component);
            if (existing != null)
            {
                Debug.LogWarning($"组件 {component.name} 已经在收集列表中");
                return;
            }
            
            var componentInfo = new ComponentInfo(name, component, path);
            collectedComponents.Add(componentInfo);
            
            Debug.Log($"已添加组件到收集列表: {name} ({component.GetType().Name})");
        }
        
        /// <summary>
        /// 移除组件从收集列表
        /// </summary>
        public void RemoveComponent(Component component)
        {
            var componentInfo = collectedComponents.Find(c => c.component == component);
            if (componentInfo != null)
            {
                collectedComponents.Remove(componentInfo);
                Debug.Log($"已从收集列表移除组件: {componentInfo.name}");
            }
        }
        
        /// <summary>
        /// 清空收集列表
        /// </summary>
        public void ClearComponents()
        {
            collectedComponents.Clear();
            Debug.Log("已清空组件收集列表");
        }
        
        /// <summary>
        /// 根据名称获取组件
        /// </summary>
        public Component GetComponent(string name)
        {
            var componentInfo = collectedComponents.Find(c => c.name == name);
            return componentInfo?.component;
        }
        
        /// <summary>
        /// 根据类型获取组件
        /// </summary>
        public T GetComponent<T>(string name) where T : Component
        {
            var component = GetComponent(name);
            return component as T;
        }
        
        /// <summary>
        /// 获取所有指定类型的组件
        /// </summary>
        public List<T> GetComponents<T>() where T : Component
        {
            var result = new List<T>();
            foreach (var info in collectedComponents)
            {
                if (info.component is T component)
                {
                    result.Add(component);
                }
            }
            return result;
        }
        
        /// <summary>
        /// 生成Lua绑定代码
        /// </summary>
        public string GenerateLuaBindingCode(string className)
        {
            var code = new System.Text.StringBuilder();
            
            code.AppendLine($"-- {className} 组件绑定");
            code.AppendLine($"function {className}:InitComponents()");
            code.AppendLine("    if not self.gameObject then");
            code.AppendLine("        print(\"GameObject is nil, cannot init components\")");
            code.AppendLine("        return");
            code.AppendLine("    end");
            code.AppendLine();
            code.AppendLine("    -- 获取组件收集器");
            code.AppendLine("    local collector = self.gameObject:GetComponent(typeof(CS.GameFramework.XLua.UIComponentCollector))");
            code.AppendLine("    if not collector then");
            code.AppendLine("        print(\"UIComponentCollector not found\")");
            code.AppendLine("        return");
            code.AppendLine("    end");
            code.AppendLine();
            
            foreach (var info in collectedComponents)
            {
                string luaVarName = info.name.Replace(" ", "_").Replace("-", "_");
                code.AppendLine($"    -- {info.componentType}: {info.path}");
                code.AppendLine($"    self.{luaVarName} = collector:GetComponent(\"{info.name}\")");
            }
            
            code.AppendLine("end");
            
            return code.ToString();
        }
        
        /// <summary>
        /// 验证收集的组件是否有效
        /// </summary>
        public void ValidateComponents()
        {
            for (int i = collectedComponents.Count - 1; i >= 0; i--)
            {
                if (collectedComponents[i].component == null)
                {
                    Debug.LogWarning($"移除无效组件: {collectedComponents[i].name}");
                    collectedComponents.RemoveAt(i);
                }
            }
        }
        
        private void OnValidate()
        {
            ValidateComponents();
        }
    }
}
