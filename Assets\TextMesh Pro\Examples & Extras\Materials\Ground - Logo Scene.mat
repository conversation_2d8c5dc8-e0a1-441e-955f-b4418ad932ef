%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Ground - Logo Scene
  m_Shader: {fileID: 46, guid: 0000000000000000f000000000000000, type: 0}
  m_ShaderKeywords: _NORMALMAP
  m_LightmapFlags: 5
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
      data:
        first:
          name: _MainTex
        second:
          m_Texture: {fileID: 2800000, guid: 1cdc5b506b1a4a33a53c30669ced1f51, type: 3}
          m_Scale: {x: 20, y: 20}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _BumpMap
        second:
          m_Texture: {fileID: 2800000, guid: 8b8c8a10edf94ddc8cc4cc4fcd5696a9, type: 3}
          m_Scale: {x: 30, y: 50}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _DetailNormalMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _ParallaxMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _OcclusionMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _EmissionMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _DetailMask
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _DetailAlbedoMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _MetallicGlossMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _BorderTex
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _FillTex
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _EdgeTex
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
    m_Floats:
      data:
        first:
          name: _SrcBlend
        second: 1
      data:
        first:
          name: _DstBlend
        second: 0
      data:
        first:
          name: _Radius
        second: 0
      data:
        first:
          name: _Cutoff
        second: .5
      data:
        first:
          name: _Shininess
        second: .220354751
      data:
        first:
          name: _Parallax
        second: .0199999996
      data:
        first:
          name: _ZWrite
        second: 1
      data:
        first:
          name: _Glossiness
        second: .344000012
      data:
        first:
          name: _BumpScale
        second: 1
      data:
        first:
          name: _OcclusionStrength
        second: 1
      data:
        first:
          name: _DetailNormalMapScale
        second: 1
      data:
        first:
          name: _UVSec
        second: 0
      data:
        first:
          name: _Mode
        second: 0
      data:
        first:
          name: _Metallic
        second: 0
      data:
        first:
          name: _EmissionScaleUI
        second: 0
      data:
        first:
          name: _EdgeSoftness
        second: 0
      data:
        first:
          name: _DiffusePower
        second: 1
      data:
        first:
          name: _Border
        second: .0214285739
      data:
        first:
          name: _Size
        second: .100000001
      data:
        first:
          name: _EdgeWidth
        second: 0
    m_Colors:
      data:
        first:
          name: _EmissionColor
        second: {r: 0, g: 0, b: 0, a: 0}
      data:
        first:
          name: _Color
        second: {r: 1, g: 1, b: 1, a: 1}
      data:
        first:
          name: _SpecColor
        second: {r: .5, g: .5, b: .5, a: 1}
      data:
        first:
          name: _EmissionColorUI
        second: {r: 1, g: 1, b: 1, a: 1}
      data:
        first:
          name: _FaceColor
        second: {r: 1, g: 1, b: 1, a: 1}
      data:
        first:
          name: _BorderColor
        second: {r: 0, g: 0, b: 0, a: 1}
