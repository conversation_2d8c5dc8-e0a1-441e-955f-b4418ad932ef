using UnityEngine;
using UnityEditor;
using GameFramework.XLua;
using System.Reflection;

public class ComponentContextMenu
{
    [MenuItem("CONTEXT/Component/添加到组件收集列表")]
    private static void AddToComponentCollector(MenuCommand command)
    {
        Component component = command.context as Component;
        if (component == null) return;
        
        // 获取prefab根对象
        GameObject rootObject = GetPrefabRoot(component.gameObject);
        if (rootObject == null)
        {
            Debug.LogError("无法找到prefab根对象");
            return;
        }
        
        // 检查是否已有UIComponentCollector组件
        UIComponentCollector collector = rootObject.GetComponent<UIComponentCollector>();
        if (collector == null)
        {
            // 创建UIComponentCollector组件
            collector = rootObject.AddComponent<UIComponentCollector>();
            Debug.Log($"已在 {rootObject.name} 上创建 UIComponentCollector 组件");
        }
        
        // 生成组件路径
        string componentPath = GetComponentPath(component.transform, rootObject.transform);
        
        // 生成组件名称
        string componentName = GenerateComponentName(component);
        
        // 添加组件到收集列表
        collector.AddComponent(componentName, component, componentPath);
        
        // 标记为已修改
        EditorUtility.SetDirty(collector);
        EditorUtility.SetDirty(rootObject);
        
        Debug.Log($"已添加组件 {componentName} 到收集列表");
    }
    
    [MenuItem("CONTEXT/Component/添加到组件收集列表", true)]
    private static bool ValidateAddToComponentCollector(MenuCommand command)
    {
        Component component = command.context as Component;
        if (component == null) return false;
        
        // 检查是否是UI相关组件
        return IsUIComponent(component);
    }
    
    private static GameObject GetPrefabRoot(GameObject obj)
    {
        // 向上查找直到找到prefab根对象
        Transform current = obj.transform;
        GameObject prefabRoot = null;
        
        while (current != null)
        {
            // 检查是否是prefab实例的根
            if (PrefabUtility.IsPartOfPrefabInstance(current.gameObject))
            {
                GameObject prefabAsset = PrefabUtility.GetCorrespondingObjectFromSource(current.gameObject);
                if (prefabAsset != null)
                {
                    prefabRoot = current.gameObject;
                }
            }
            // 检查是否是prefab资源
            else if (PrefabUtility.IsPartOfPrefabAsset(current.gameObject))
            {
                prefabRoot = current.gameObject;
            }
            
            current = current.parent;
        }
        
        // 如果没找到prefab根，返回场景中的根对象
        if (prefabRoot == null)
        {
            current = obj.transform;
            while (current.parent != null)
            {
                current = current.parent;
            }
            prefabRoot = current.gameObject;
        }
        
        return prefabRoot;
    }
    
    private static string GetComponentPath(Transform target, Transform root)
    {
        if (target == root) return "";
        
        string path = target.name;
        Transform current = target.parent;
        
        while (current != null && current != root)
        {
            path = current.name + "/" + path;
            current = current.parent;
        }
        
        return path;
    }
    
    private static string GenerateComponentName(Component component)
    {
        string typeName = component.GetType().Name;
        string objectName = component.gameObject.name;
        
        // 移除常见的UI前缀/后缀
        objectName = objectName.Replace("(Clone)", "").Trim();
        
        // 根据组件类型生成合适的名称
        switch (typeName)
        {
            case "Button":
                return objectName.EndsWith("Button") ? objectName : objectName + "Button";
            case "Text":
            case "TextMeshProUGUI":
                return objectName.EndsWith("Text") ? objectName : objectName + "Text";
            case "Image":
                return objectName.EndsWith("Image") ? objectName : objectName + "Image";
            case "InputField":
            case "TMP_InputField":
                return objectName.EndsWith("Input") ? objectName : objectName + "Input";
            case "Slider":
                return objectName.EndsWith("Slider") ? objectName : objectName + "Slider";
            case "Toggle":
                return objectName.EndsWith("Toggle") ? objectName : objectName + "Toggle";
            case "Dropdown":
            case "TMP_Dropdown":
                return objectName.EndsWith("Dropdown") ? objectName : objectName + "Dropdown";
            case "ScrollRect":
                return objectName.EndsWith("ScrollView") ? objectName : objectName + "ScrollView";
            case "RectTransform":
                return objectName + "Transform";
            default:
                return objectName + typeName;
        }
    }
    
    private static bool IsUIComponent(Component component)
    {
        if (component == null) return false;
        
        System.Type componentType = component.GetType();
        
        // 检查是否是UI组件
        return componentType.Namespace != null && 
               (componentType.Namespace.Contains("UnityEngine.UI") ||
                componentType.Namespace.Contains("TMPro") ||
                componentType == typeof(RectTransform) ||
                componentType == typeof(CanvasGroup) ||
                componentType == typeof(Canvas) ||
                componentType == typeof(CanvasRenderer));
    }
}
