---@meta
---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#17:14"
---@class Tutorial.BaseClass: object
---@overload fun(): Tutorial.BaseClass
local BaseClass = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#24:20"
---@type integer
BaseClass.BSF = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#31:13"
---@type integer
BaseClass.BMF = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#19:21"
---@return void
function BaseClass.BSFunc()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#26:14"
---@return void
function BaseClass:BMFunc()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#168:20"
---@return integer
function BaseClass:GetSomeBaseData()
end


---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#41:13"
---@enum Tutorial.TestEnum.Detail
local TestEnum = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#43:2"
---@type integer
TestEnum.E1 = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#44:2"
---@type integer
TestEnum.E2 = nil

---@enum (key) Tutorial.TestEnum
---| CS.TestEnum.E1
---| CS.TestEnum.E2

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#49:14"
---@class Tutorial.DerivedClass: Tutorial.BaseClass
---@overload fun(): Tutorial.DerivedClass
local DerivedClass = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#63:13"
---@type integer
DerivedClass.DMF = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#115:24"
---@type System.Action<string>
DerivedClass.TestDelegate = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#58:14"
---@return void
function DerivedClass:DMFunc()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#65:16"
---@param p1 Tutorial.Param1
---@param p2 integer
---@param luafunc System.Action
---@return numberint, string, System.Action
function DerivedClass:ComplexFunc(p1, p2, p3, luafunc, csfunc)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#78:14"
---@param i integer
---@return void
function DerivedClass:TestFunc(i)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#83:14"
---@param i string
---@return void
function DerivedClass:TestFunc(i)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#88:38"
---@param a Tutorial.DerivedClass
---@param b Tutorial.DerivedClass
---@return Tutorial.DerivedClass
function DerivedClass.op_Addition(a, b)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#95:14"
---@param a integer
---@param b string
---@param c string
---@return void
function DerivedClass:DefaultValueFunc(a, b, c)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#100:14"
---@param a integer
---@param strs string[]
---@return void
function DerivedClass:VariableParamsFunc(a, strs)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#109:18"
---@param e Tutorial.TestEnum
---@return Tutorial.TestEnum
function DerivedClass:EnumTestFunc(e)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#120:22"
---@param value System.Action
---@return void
function DerivedClass:add_TestEvent(value)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#120:22"
---@param value System.Action
---@return void
function DerivedClass:remove_TestEvent(value)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#122:14"
---@return void
function DerivedClass:CallEvent()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#127:15"
---@param n long
---@return ulong
function DerivedClass:TestLong(n)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#142:15"
---@return Tutorial.ICalc
function DerivedClass:GetCalc()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#147:14"
---@return void
function DerivedClass:GenericMethod()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#162:20"
---@return integer
function DerivedClass:GetSomeData()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#174:21"
---@return void
function DerivedClass:GenericMethodOfString()
end


---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#52:14"
---@enum Tutorial.DerivedClass.TestEnumInner.Detail
local TestEnumInner = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#54:3"
---@type integer
TestEnumInner.E3 = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#55:3"
---@type integer
TestEnumInner.E4 = nil

---@enum (key) Tutorial.DerivedClass.TestEnumInner
---| CS.TestEnumInner.E3
---| CS.TestEnumInner.E4

---@interface ICalc

---@source "file:///f:/Unity/Unity6/My project/Assets/XLua/Tutorial/LuaCallCSharp/LuaCallCs.cs#160:21"
---@class Tutorial.DerivedClassExtensions: object
---@overload fun(): Tutorial.DerivedClassExtensions
local DerivedClassExtensions = {}

---@class System.Type: System.Reflection.MemberInfo, System.Reflection.ICustomAttributeProvider, System.Reflection.IReflect
---@overload fun(): System.Type
local Type = {}
---@type char
Type.Delimiter = nil

---@type System.Type[]
Type.EmptyTypes = nil

---@type System.Reflection.MemberFilter
Type.FilterAttribute = nil

---@type System.Reflection.MemberFilter
Type.FilterName = nil

---@type System.Reflection.MemberFilter
Type.FilterNameIgnoreCase = nil

---@type table
Type.Missing = nil

---@type System.Reflection.Assembly
Type.Assembly = nil

---@type string
Type.AssemblyQualifiedName = nil

---@type System.Reflection.TypeAttributes
Type.Attributes = nil

---@type System.Type
Type.BaseType = nil

---@type boolean
Type.ContainsGenericParameters = nil

---@type System.Reflection.MethodBase
Type.DeclaringMethod = nil

---@type System.Type
Type.DeclaringType = nil

---@type System.Reflection.Binder
Type.DefaultBinder = nil

---@type string
Type.FullName = nil

---@type System.Reflection.GenericParameterAttributes
Type.GenericParameterAttributes = nil

---@type integer
Type.GenericParameterPosition = nil

---@type System.Type[]
Type.GenericTypeArguments = nil

---@type System.Guid
Type.GUID = nil

---@type boolean
Type.HasElementType = nil

---@type boolean
Type.IsAbstract = nil

---@type boolean
Type.IsAnsiClass = nil

---@type boolean
Type.IsArray = nil

---@type boolean
Type.IsAutoClass = nil

---@type boolean
Type.IsAutoLayout = nil

---@type boolean
Type.IsByRef = nil

---@type boolean
Type.IsByRefLike = nil

---@type boolean
Type.IsClass = nil

---@type boolean
Type.IsCOMObject = nil

---@type boolean
Type.IsConstructedGenericType = nil

---@type boolean
Type.IsContextful = nil

---@type boolean
Type.IsEnum = nil

---@type boolean
Type.IsExplicitLayout = nil

---@type boolean
Type.IsGenericMethodParameter = nil

---@type boolean
Type.IsGenericParameter = nil

---@type boolean
Type.IsGenericType = nil

---@type boolean
Type.IsGenericTypeDefinition = nil

---@type boolean
Type.IsGenericTypeParameter = nil

---@type boolean
Type.IsImport = nil

---@type boolean
Type.IsInterface = nil

---@type boolean
Type.IsLayoutSequential = nil

---@type boolean
Type.IsMarshalByRef = nil

---@type boolean
Type.IsNested = nil

---@type boolean
Type.IsNestedAssembly = nil

---@type boolean
Type.IsNestedFamANDAssem = nil

---@type boolean
Type.IsNestedFamily = nil

---@type boolean
Type.IsNestedFamORAssem = nil

---@type boolean
Type.IsNestedPrivate = nil

---@type boolean
Type.IsNestedPublic = nil

---@type boolean
Type.IsNotPublic = nil

---@type boolean
Type.IsPointer = nil

---@type boolean
Type.IsPrimitive = nil

---@type boolean
Type.IsPublic = nil

---@type boolean
Type.IsSealed = nil

---@type boolean
Type.IsSecurityCritical = nil

---@type boolean
Type.IsSecuritySafeCritical = nil

---@type boolean
Type.IsSecurityTransparent = nil

---@type boolean
Type.IsSerializable = nil

---@type boolean
Type.IsSignatureType = nil

---@type boolean
Type.IsSpecialName = nil

---@type boolean
Type.IsSZArray = nil

---@type boolean
Type.IsTypeDefinition = nil

---@type boolean
Type.IsUnicodeClass = nil

---@type boolean
Type.IsValueType = nil

---@type boolean
Type.IsVariableBoundArray = nil

---@type boolean
Type.IsVisible = nil

---@type System.Reflection.MemberTypes
Type.MemberType = nil

---@type System.Reflection.Module
Type.Module = nil

---@type string
Type.Namespace = nil

---@type System.Type
Type.ReflectedType = nil

---@type System.Runtime.InteropServices.StructLayoutAttribute
Type.StructLayoutAttribute = nil

---@type System.RuntimeTypeHandle
Type.TypeHandle = nil

---@type System.Reflection.ConstructorInfo
Type.TypeInitializer = nil

---@type System.Type
Type.UnderlyingSystemType = nil

---@param o table
---@return boolean
function Type:Equals(o)
end

---@param o System.Type
---@return boolean
function Type:Equals(o)
end

---@param filter System.Reflection.TypeFilter
---@param filterCriteria table
---@return System.Type[]
function Type:FindInterfaces(filter, filterCriteria)
end

---@param memberType System.Reflection.MemberTypes
---@param bindingAttr System.Reflection.BindingFlags
---@param filter System.Reflection.MemberFilter
---@param filterCriteria table
---@return System.Reflection.MemberInfo[]
function Type:FindMembers(memberType, bindingAttr, filter, filterCriteria)
end

---@return integer
function Type:GetArrayRank()
end

---@param bindingAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param callConvention System.Reflection.CallingConventions
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.ConstructorInfo
function Type:GetConstructor(bindingAttr, binder, callConvention, types, modifiers)
end

---@param bindingAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.ConstructorInfo
function Type:GetConstructor(bindingAttr, binder, types, modifiers)
end

---@param types System.Type[]
---@return System.Reflection.ConstructorInfo
function Type:GetConstructor(types)
end

---@return System.Reflection.ConstructorInfo[]
function Type:GetConstructors()
end

---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.ConstructorInfo[]
function Type:GetConstructors(bindingAttr)
end

---@return System.Reflection.MemberInfo[]
function Type:GetDefaultMembers()
end

---@return System.Type
function Type:GetElementType()
end

---@param value table
---@return string
function Type:GetEnumName(value)
end

---@return string[]
function Type:GetEnumNames()
end

---@return System.Type
function Type:GetEnumUnderlyingType()
end

---@return System.Array
function Type:GetEnumValues()
end

---@param name string
---@return System.Reflection.EventInfo
function Type:GetEvent(name)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.EventInfo
function Type:GetEvent(name, bindingAttr)
end

---@return System.Reflection.EventInfo[]
function Type:GetEvents()
end

---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.EventInfo[]
function Type:GetEvents(bindingAttr)
end

---@param name string
---@return System.Reflection.FieldInfo
function Type:GetField(name)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.FieldInfo
function Type:GetField(name, bindingAttr)
end

---@return System.Reflection.FieldInfo[]
function Type:GetFields()
end

---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.FieldInfo[]
function Type:GetFields(bindingAttr)
end

---@return System.Type[]
function Type:GetGenericArguments()
end

---@return System.Type[]
function Type:GetGenericParameterConstraints()
end

---@return System.Type
function Type:GetGenericTypeDefinition()
end

---@return integer
function Type:GetHashCode()
end

---@param name string
---@return System.Type
function Type:GetInterface(name)
end

---@param name string
---@param ignoreCase boolean
---@return System.Type
function Type:GetInterface(name, ignoreCase)
end

---@param interfaceType System.Type
---@return System.Reflection.InterfaceMapping
function Type:GetInterfaceMap(interfaceType)
end

---@return System.Type[]
function Type:GetInterfaces()
end

---@param name string
---@return System.Reflection.MemberInfo[]
function Type:GetMember(name)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.MemberInfo[]
function Type:GetMember(name, bindingAttr)
end

---@param name string
---@param type System.Reflection.MemberTypes
---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.MemberInfo[]
function Type:GetMember(name, type, bindingAttr)
end

---@return System.Reflection.MemberInfo[]
function Type:GetMembers()
end

---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.MemberInfo[]
function Type:GetMembers(bindingAttr)
end

---@param name string
---@return System.Reflection.MethodInfo
function Type:GetMethod(name)
end

---@param name string
---@param genericParameterCount integer
---@param bindingAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param callConvention System.Reflection.CallingConventions
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, genericParameterCount, bindingAttr, binder, callConvention, types, modifiers)
end

---@param name string
---@param genericParameterCount integer
---@param bindingAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, genericParameterCount, bindingAttr, binder, types, modifiers)
end

---@param name string
---@param genericParameterCount integer
---@param types System.Type[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, genericParameterCount, types)
end

---@param name string
---@param genericParameterCount integer
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, genericParameterCount, types, modifiers)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, bindingAttr)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param callConvention System.Reflection.CallingConventions
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, bindingAttr, binder, callConvention, types, modifiers)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, bindingAttr, binder, types, modifiers)
end

---@param name string
---@param types System.Type[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, types)
end

---@param name string
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.MethodInfo
function Type:GetMethod(name, types, modifiers)
end

---@return System.Reflection.MethodInfo[]
function Type:GetMethods()
end

---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.MethodInfo[]
function Type:GetMethods(bindingAttr)
end

---@param name string
---@return System.Type
function Type:GetNestedType(name)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@return System.Type
function Type:GetNestedType(name, bindingAttr)
end

---@return System.Type[]
function Type:GetNestedTypes()
end

---@param bindingAttr System.Reflection.BindingFlags
---@return System.Type[]
function Type:GetNestedTypes(bindingAttr)
end

---@return System.Reflection.PropertyInfo[]
function Type:GetProperties()
end

---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.PropertyInfo[]
function Type:GetProperties(bindingAttr)
end

---@param name string
---@return System.Reflection.PropertyInfo
function Type:GetProperty(name)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@return System.Reflection.PropertyInfo
function Type:GetProperty(name, bindingAttr)
end

---@param name string
---@param bindingAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param returnType System.Type
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.PropertyInfo
function Type:GetProperty(name, bindingAttr, binder, returnType, types, modifiers)
end

---@param name string
---@param returnType System.Type
---@return System.Reflection.PropertyInfo
function Type:GetProperty(name, returnType)
end

---@param name string
---@param returnType System.Type
---@param types System.Type[]
---@return System.Reflection.PropertyInfo
function Type:GetProperty(name, returnType, types)
end

---@param name string
---@param returnType System.Type
---@param types System.Type[]
---@param modifiers System.Reflection.ParameterModifier[]
---@return System.Reflection.PropertyInfo
function Type:GetProperty(name, returnType, types, modifiers)
end

---@param name string
---@param types System.Type[]
---@return System.Reflection.PropertyInfo
function Type:GetProperty(name, types)
end

---@return System.Type
function Type:GetType()
end

---@param typeName string
---@return System.Type
function Type.GetType(typeName)
end

---@param typeName string
---@param throwOnError boolean
---@return System.Type
function Type.GetType(typeName, throwOnError)
end

---@param typeName string
---@param throwOnError boolean
---@param ignoreCase boolean
---@return System.Type
function Type.GetType(typeName, throwOnError, ignoreCase)
end

---@param typeName string
---@param assemblyResolver System.Func<System.Reflection.AssemblyName, System.Reflection.Assembly>
---@param typeResolver System.Func<System.Reflection.Assembly, string, bool, System.Type>
---@return System.Type
function Type.GetType(typeName, assemblyResolver, typeResolver)
end

---@param typeName string
---@param assemblyResolver System.Func<System.Reflection.AssemblyName, System.Reflection.Assembly>
---@param typeResolver System.Func<System.Reflection.Assembly, string, bool, System.Type>
---@param throwOnError boolean
---@return System.Type
function Type.GetType(typeName, assemblyResolver, typeResolver, throwOnError)
end

---@param typeName string
---@param assemblyResolver System.Func<System.Reflection.AssemblyName, System.Reflection.Assembly>
---@param typeResolver System.Func<System.Reflection.Assembly, string, bool, System.Type>
---@param throwOnError boolean
---@param ignoreCase boolean
---@return System.Type
function Type.GetType(typeName, assemblyResolver, typeResolver, throwOnError, ignoreCase)
end

---@param args object[]
---@return System.Type[]
function Type.GetTypeArray(args)
end

---@param type System.Type
---@return System.TypeCode
function Type.GetTypeCode(type)
end

---@param clsid System.Guid
---@return System.Type
function Type.GetTypeFromCLSID(clsid)
end

---@param clsid System.Guid
---@param throwOnError boolean
---@return System.Type
function Type.GetTypeFromCLSID(clsid, throwOnError)
end

---@param clsid System.Guid
---@param server string
---@return System.Type
function Type.GetTypeFromCLSID(clsid, server)
end

---@param clsid System.Guid
---@param server string
---@param throwOnError boolean
---@return System.Type
function Type.GetTypeFromCLSID(clsid, server, throwOnError)
end

---@param handle System.RuntimeTypeHandle
---@return System.Type
function Type.GetTypeFromHandle(handle)
end

---@param progID string
---@return System.Type
function Type.GetTypeFromProgID(progID)
end

---@param progID string
---@param throwOnError boolean
---@return System.Type
function Type.GetTypeFromProgID(progID, throwOnError)
end

---@param progID string
---@param server string
---@return System.Type
function Type.GetTypeFromProgID(progID, server)
end

---@param progID string
---@param server string
---@param throwOnError boolean
---@return System.Type
function Type.GetTypeFromProgID(progID, server, throwOnError)
end

---@param o table
---@return System.RuntimeTypeHandle
function Type.GetTypeHandle(o)
end

---@param name string
---@param invokeAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param target table
---@param args object[]
---@return table
function Type:InvokeMember(name, invokeAttr, binder, target, args)
end

---@param name string
---@param invokeAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param target table
---@param args object[]
---@param culture System.Globalization.CultureInfo
---@return table
function Type:InvokeMember(name, invokeAttr, binder, target, args, culture)
end

---@param name string
---@param invokeAttr System.Reflection.BindingFlags
---@param binder System.Reflection.Binder
---@param target table
---@param args object[]
---@param modifiers System.Reflection.ParameterModifier[]
---@param culture System.Globalization.CultureInfo
---@param namedParameters string[]
---@return table
function Type:InvokeMember(name, invokeAttr, binder, target, args, modifiers, culture, namedParameters)
end

---@param c System.Type
---@return boolean
function Type:IsAssignableFrom(c)
end

---@param value table
---@return boolean
function Type:IsEnumDefined(value)
end

---@param other System.Type
---@return boolean
function Type:IsEquivalentTo(other)
end

---@param o table
---@return boolean
function Type:IsInstanceOfType(o)
end

---@param c System.Type
---@return boolean
function Type:IsSubclassOf(c)
end

---@return System.Type
function Type:MakeArrayType()
end

---@param rank integer
---@return System.Type
function Type:MakeArrayType(rank)
end

---@return System.Type
function Type:MakeByRefType()
end

---@param position integer
---@return System.Type
function Type.MakeGenericMethodParameter(position)
end

---@param typeArguments System.Type[]
---@return System.Type
function Type:MakeGenericType(typeArguments)
end

---@return System.Type
function Type:MakePointerType()
end

---@param left System.Type
---@param right System.Type
---@return boolean
function Type.op_Equality(left, right)
end

---@param left System.Type
---@param right System.Type
---@return boolean
function Type.op_Inequality(left, right)
end

---@param typeName string
---@param throwIfNotFound boolean
---@param ignoreCase boolean
---@return System.Type
function Type.ReflectionOnlyGetType(typeName, throwIfNotFound, ignoreCase)
end

---@return string
function Type:ToString()
end


---Base class for all objects Unity can reference.
---@class UnityEngine.Object: object
---@overload fun(): UnityEngine.Object
local Object = {}
---The name of the object.
---@type string
Object.name = nil

---Should the object be hidden, saved with the Scene or modifiable by the user?
---@type UnityEngine.HideFlags
Object.hideFlags = nil

---Returns the instance ID of the object.
---@return integer
function Object:GetInstanceID()
end

---@return integer
function Object:GetHashCode()
end

---@param other table
---@return boolean
function Object:Equals(other)
end

---@param exists UnityEngine.Object
---@return boolean
function Object.op_Implicit(exists)
end

---@param original T
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original)
end

---@param original T
---@param parent UnityEngine.Transform
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, parent)
end

---@param original T
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, position, rotation)
end

---@param original T
---@param parent UnityEngine.Transform
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, parent, position, rotation)
end

---@param original T
---@param count integer
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count)
end

---@param original T
---@param count integer
---@param parent UnityEngine.Transform
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, parent)
end

---@param original T
---@param count integer
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, position, rotation)
end

---@param original T
---@param count integer
---@param positions System.ReadOnlySpan<UnityEngine.Vector3>
---@param rotations System.ReadOnlySpan<UnityEngine.Quaternion>
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, positions, rotations)
end

---@param original T
---@param count integer
---@param parent UnityEngine.Transform
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, parent, position, rotation)
end

---@param original T
---@param count integer
---@param parent UnityEngine.Transform
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@param cancellationToken System.Threading.CancellationToken
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, parent, position, rotation, cancellationToken)
end

---@param original T
---@param count integer
---@param parent UnityEngine.Transform
---@param positions System.ReadOnlySpan<UnityEngine.Vector3>
---@param rotations System.ReadOnlySpan<UnityEngine.Quaternion>
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, parent, positions, rotations)
end

---@param original T
---@param count integer
---@param parent UnityEngine.Transform
---@param positions System.ReadOnlySpan<UnityEngine.Vector3>
---@param rotations System.ReadOnlySpan<UnityEngine.Quaternion>
---@param cancellationToken System.Threading.CancellationToken
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, parent, positions, rotations, cancellationToken)
end

---@param original T
---@param parameters UnityEngine.InstantiateParameters
---@param cancellationToken System.Threading.CancellationToken
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, parameters, cancellationToken)
end

---@param original T
---@param count integer
---@param parameters UnityEngine.InstantiateParameters
---@param cancellationToken System.Threading.CancellationToken
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, parameters, cancellationToken)
end

---@param original T
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@param parameters UnityEngine.InstantiateParameters
---@param cancellationToken System.Threading.CancellationToken
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, position, rotation, parameters, cancellationToken)
end

---@param original T
---@param count integer
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@param parameters UnityEngine.InstantiateParameters
---@param cancellationToken System.Threading.CancellationToken
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, position, rotation, parameters, cancellationToken)
end

---@param original T
---@param count integer
---@param positions System.ReadOnlySpan<UnityEngine.Vector3>
---@param rotations System.ReadOnlySpan<UnityEngine.Quaternion>
---@param parameters UnityEngine.InstantiateParameters
---@param cancellationToken System.Threading.CancellationToken
---@return UnityEngine.AsyncInstantiateOperation<T>
function Object.InstantiateAsync(original, count, positions, rotations, parameters, cancellationToken)
end

---The instantiated clone.
---@param original UnityEngine.Object An existing object that you want to make a copy of.
---@param position UnityEngine.Vector3 Position for the new object.
---@param rotation UnityEngine.Quaternion Orientation of the new object.
---@return UnityEngine.Object
function Object.Instantiate(original, position, rotation)
end

---The instantiated clone.
---@param original UnityEngine.Object An existing object that you want to make a copy of.
---@param position UnityEngine.Vector3 Position for the new object.
---@param rotation UnityEngine.Quaternion Orientation of the new object.
---@param parent UnityEngine.Transform Parent that will be assigned to the new object.
---@return UnityEngine.Object
function Object.Instantiate(original, position, rotation, parent)
end

---The instantiated clone.
---@param original UnityEngine.Object An existing object that you want to make a copy of.
---@return UnityEngine.Object
function Object.Instantiate(original)
end

---The instantiated clone.
---@param original UnityEngine.Object An existing object that you want to make a copy of.
---@param scene UnityEngine.SceneManagement.Scene
---@return UnityEngine.Object
function Object.Instantiate(original, scene)
end

---@param original T
---@param parameters UnityEngine.InstantiateParameters
---@return T
function Object.Instantiate(original, parameters)
end

---@param original T
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@param parameters UnityEngine.InstantiateParameters
---@return T
function Object.Instantiate(original, position, rotation, parameters)
end

---The instantiated clone.
---@param original UnityEngine.Object An existing object that you want to make a copy of.
---@param parent UnityEngine.Transform Parent that will be assigned to the new object.
---@return UnityEngine.Object
function Object.Instantiate(original, parent)
end

---The instantiated clone.
---@param original UnityEngine.Object An existing object that you want to make a copy of.
---@param parent UnityEngine.Transform Parent that will be assigned to the new object.
---@param instantiateInWorldSpace boolean When you assign a parent Object, pass true to position the new object directly in world space. Pass false to set the Object’s position relative to its new parent.
---@return UnityEngine.Object
function Object.Instantiate(original, parent, instantiateInWorldSpace)
end

---@param original T
---@return T
function Object.Instantiate(original)
end

---@param original T
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@return T
function Object.Instantiate(original, position, rotation)
end

---@param original T
---@param position UnityEngine.Vector3
---@param rotation UnityEngine.Quaternion
---@param parent UnityEngine.Transform
---@return T
function Object.Instantiate(original, position, rotation, parent)
end

---@param original T
---@param parent UnityEngine.Transform
---@return T
function Object.Instantiate(original, parent)
end

---@param original T
---@param parent UnityEngine.Transform
---@param worldPositionStays boolean
---@return T
function Object.Instantiate(original, parent, worldPositionStays)
end

---Removes a GameObject, component or asset.
---@param obj UnityEngine.Object The object to destroy.
---@param t number The optional amount of time to delay before destroying the object.
---@return void
function Object.Destroy(obj, t)
end

---Removes a GameObject, component or asset.
---@param obj UnityEngine.Object The object to destroy.
---@return void
function Object.Destroy(obj)
end

---Destroys the object obj immediately. You are strongly recommended to use Destroy instead.
---@param obj UnityEngine.Object Object to be destroyed.
---@param allowDestroyingAssets boolean Set to true to allow assets to be destroyed.
---@return void
function Object.DestroyImmediate(obj, allowDestroyingAssets)
end

---Destroys the object obj immediately. You are strongly recommended to use Destroy instead.
---@param obj UnityEngine.Object Object to be destroyed.
---@return void
function Object.DestroyImmediate(obj)
end

---The array of objects found matching the type specified.
---@param type System.Type The type of object to find.
---@return UnityEngine.Object[]
function Object.FindObjectsOfType(type)
end

---The array of objects found matching the type specified.
---@param type System.Type The type of object to find.
---@param includeInactive boolean If true, components attached to inactive GameObjects are also included.
---@return UnityEngine.Object[]
function Object.FindObjectsOfType(type, includeInactive)
end

---The array of objects found matching the type specified.
---@param type System.Type The type of object to find.
---@param sortMode UnityEngine.FindObjectsSortMode Whether and how to sort the returned array. Not sorting the array makes this function run significantly faster.
---@return UnityEngine.Object[]
function Object.FindObjectsByType(type, sortMode)
end

---The array of objects found matching the type specified.
---@param type System.Type The type of object to find.
---@param findObjectsInactive UnityEngine.FindObjectsInactive Whether to include components attached to inactive GameObjects. If you don't specify this parameter, this function doesn't include inactive objects in the results.
---@param sortMode UnityEngine.FindObjectsSortMode Whether and how to sort the returned array. Not sorting the array makes this function run significantly faster.
---@return UnityEngine.Object[]
function Object.FindObjectsByType(type, findObjectsInactive, sortMode)
end

---Do not destroy the target Object when loading a new Scene.
---@param target UnityEngine.Object An Object not destroyed on Scene change.
---@return void
function Object.DontDestroyOnLoad(target)
end

---@param obj UnityEngine.Object
---@param t number
---@return void
function Object.DestroyObject(obj, t)
end

---@param obj UnityEngine.Object
---@return void
function Object.DestroyObject(obj)
end

---@param type System.Type
---@return UnityEngine.Object[]
function Object.FindSceneObjectsOfType(type)
end

---The array of objects and assets found matching the type specified.
---@param type System.Type The type of object or asset to find.
---@return UnityEngine.Object[]
function Object.FindObjectsOfTypeIncludingAssets(type)
end

---@return T[]
function Object.FindObjectsOfType()
end

---@param sortMode UnityEngine.FindObjectsSortMode
---@return T[]
function Object.FindObjectsByType(sortMode)
end

---@param includeInactive boolean
---@return T[]
function Object.FindObjectsOfType(includeInactive)
end

---@param findObjectsInactive UnityEngine.FindObjectsInactive
---@param sortMode UnityEngine.FindObjectsSortMode
---@return T[]
function Object.FindObjectsByType(findObjectsInactive, sortMode)
end

---@return T
function Object.FindObjectOfType()
end

---@param includeInactive boolean
---@return T
function Object.FindObjectOfType(includeInactive)
end

---@return T
function Object.FindFirstObjectByType()
end

---@return T
function Object.FindAnyObjectByType()
end

---@param findObjectsInactive UnityEngine.FindObjectsInactive
---@return T
function Object.FindFirstObjectByType(findObjectsInactive)
end

---@param findObjectsInactive UnityEngine.FindObjectsInactive
---@return T
function Object.FindAnyObjectByType(findObjectsInactive)
end

---The array of objects found matching the type specified.
---@param type System.Type The type of object to find.
---@return UnityEngine.Object[]
function Object.FindObjectsOfTypeAll(type)
end

---Object The first active loaded object that matches the specified type. It returns null if no Object matches the type.
---@param type System.Type The type of object to find.
---@return UnityEngine.Object
function Object.FindObjectOfType(type)
end

---Returns the first active loaded object that matches the specified type. If no object matches the specified type, returns null.
---@param type System.Type The type of object to find.
---@return UnityEngine.Object
function Object.FindFirstObjectByType(type)
end

---Returns an arbitrary active loaded object that matches the specified type. If no object matches the specified type, returns null.
---@param type System.Type The type of object to find.
---@return UnityEngine.Object
function Object.FindAnyObjectByType(type)
end

---Object The first active loaded object that matches the specified type. It returns null if no Object matches the type.
---@param type System.Type The type of object to find.
---@param includeInactive boolean
---@return UnityEngine.Object
function Object.FindObjectOfType(type, includeInactive)
end

---Returns the first active loaded object that matches the specified type. If no object matches the specified type, returns null.
---@param type System.Type The type of object to find.
---@param findObjectsInactive UnityEngine.FindObjectsInactive Whether to include components attached to inactive GameObjects. If you don't specify this parameter, this function doesn't include inactive objects in the results.
---@return UnityEngine.Object
function Object.FindFirstObjectByType(type, findObjectsInactive)
end

---Returns an arbitrary active loaded object that matches the specified type. If no object matches the specified type, returns null.
---@param type System.Type The type of object to find.
---@param findObjectsInactive UnityEngine.FindObjectsInactive Whether to include components attached to inactive GameObjects. If you don't specify this parameter, this function doesn't include inactive objects in the results.
---@return UnityEngine.Object
function Object.FindAnyObjectByType(type, findObjectsInactive)
end

---The name returned by ToString.
---@return string
function Object:ToString()
end

---@param x UnityEngine.Object
---@param y UnityEngine.Object
---@return boolean
function Object.op_Equality(x, y)
end

---@param x UnityEngine.Object
---@param y UnityEngine.Object
---@return boolean
function Object.op_Inequality(x, y)
end


---Base class for all objects that can exist in a scene. Add components to a GameObject to control its appearance and behavior.
---@class UnityEngine.GameObject: UnityEngine.Object
---@overload fun(name: string): UnityEngine.GameObject
---@overload fun(): UnityEngine.GameObject
---@overload fun(name: string,components: System.Type[]): UnityEngine.GameObject
local GameObject = {}
---The Transform attached to the GameObject (Read Only).
---@type UnityEngine.Transform
GameObject.transform = nil

---Integer identifying the layer the GameObject is assigned to.
---@type integer
GameObject.layer = nil

---@type boolean
GameObject.active = nil

---The local active state of the GameObject. True if active, false if inactive. (Read Only)
---@type boolean
GameObject.activeSelf = nil

---The active state of the GameObject in the Scene hierarchy. True if active, false if inactive. (Read Only)
---@type boolean
GameObject.activeInHierarchy = nil

---Whether there are any Static Editor Flags set for the GameObject.
---@type boolean
GameObject.isStatic = nil

---The tag assigned to the GameObject.
---@type string
GameObject.tag = nil

---The Scene that contains the GameObject.
---@type UnityEngine.SceneManagement.Scene
GameObject.scene = nil

---The Scene culling mask defined for the GameObject. (Read Only)
---@type ulong
GameObject.sceneCullingMask = nil

---@type UnityEngine.GameObject
GameObject.gameObject = nil

---The Rigidbody attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.rigidbody = nil

---The Rigidbody2D component attached to this GameObject. (Read Only)
---@type UnityEngine.Component
GameObject.rigidbody2D = nil

---The Camera attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.camera = nil

---The Light attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.light = nil

---The Animation attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.animation = nil

---The ConstantForce attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.constantForce = nil

---The Renderer attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.renderer = nil

---The AudioSource attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.audio = nil

---The NetworkView attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.networkView = nil

---The Collider attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.collider = nil

---The Collider2D component attached to this object.
---@type UnityEngine.Component
GameObject.collider2D = nil

---The HingeJoint attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.hingeJoint = nil

---The ParticleSystem attached to this GameObject (Read Only). (Null if there is none attached).
---@type UnityEngine.Component
GameObject.particleSystem = nil

---Creates a GameObject of the specified PrimtiveType with a mesh renderer and appropriate collider.
---@param type UnityEngine.PrimitiveType The type of primitive object to create, specified as a member of the PrimitiveType enum.
---@return UnityEngine.GameObject
function GameObject.CreatePrimitive(type)
end

---@return T
function GameObject:GetComponent()
end

---A reference to a component of the specified type, returned as a Component type. If no component is found, returns null.
---@param type System.Type The type of component to search for, specified as a Type object.
---@return UnityEngine.Component
function GameObject:GetComponent(type)
end

---A reference to a component of the specified type, returned as a Component type. If no component is found, returns null.
---@param type string The name of the type of component to search for, specified as a string.
---@return UnityEngine.Component
function GameObject:GetComponent(type)
end

---A component of the matching type, if found.
---@param type System.Type The type of Component to retrieve.
---@param includeInactive boolean Whether to include inactive child GameObjects in the search.
---@return UnityEngine.Component
function GameObject:GetComponentInChildren(type, includeInactive)
end

---A component of the matching type, if found.
---@param type System.Type The type of Component to retrieve.
---@return UnityEngine.Component
function GameObject:GetComponentInChildren(type)
end

---@return T
function GameObject:GetComponentInChildren()
end

---@param includeInactive boolean
---@return T
function GameObject:GetComponentInChildren(includeInactive)
end

---A Component of the matching type, otherwise null if no matching Component is found.
---@param type System.Type The type of component to search for.
---@param includeInactive boolean Whether to include inactive parent GameObjects in the search.
---@return UnityEngine.Component
function GameObject:GetComponentInParent(type, includeInactive)
end

---A Component of the matching type, otherwise null if no matching Component is found.
---@param type System.Type The type of component to search for.
---@return UnityEngine.Component
function GameObject:GetComponentInParent(type)
end

---@return T
function GameObject:GetComponentInParent()
end

---@param includeInactive boolean
---@return T
function GameObject:GetComponentInParent(includeInactive)
end

---An array containing all matching components of type type.
---@param type System.Type The type of component to search for.
---@return UnityEngine.Component[]
function GameObject:GetComponents(type)
end

---@return T[]
function GameObject:GetComponents()
end

---@param type System.Type
---@param results System.Collections.Generic.List<UnityEngine.Component>
---@return void
function GameObject:GetComponents(type, results)
end

---@param results System.Collections.Generic.List<T>
---@return void
function GameObject:GetComponents(results)
end

---An array of all found components matching the specified type.
---@param type System.Type The type of component to search for.
---@return UnityEngine.Component[]
function GameObject:GetComponentsInChildren(type)
end

---An array of all found components matching the specified type.
---@param type System.Type The type of component to search for.
---@param includeInactive boolean Whether to include inactive child GameObjects in the search.
---@return UnityEngine.Component[]
function GameObject:GetComponentsInChildren(type, includeInactive)
end

---@param includeInactive boolean
---@return T[]
function GameObject:GetComponentsInChildren(includeInactive)
end

---@param includeInactive boolean
---@param results System.Collections.Generic.List<T>
---@return void
function GameObject:GetComponentsInChildren(includeInactive, results)
end

---@return T[]
function GameObject:GetComponentsInChildren()
end

---@param results System.Collections.Generic.List<T>
---@return void
function GameObject:GetComponentsInChildren(results)
end

---@param type System.Type
---@return UnityEngine.Component[]
function GameObject:GetComponentsInParent(type)
end

---An array of all found components matching the specified type.
---@param type System.Type The type of component to search for.
---@param includeInactive boolean Whether to include inactive parent GameObjects in the search.
---@return UnityEngine.Component[]
function GameObject:GetComponentsInParent(type, includeInactive)
end

---@param includeInactive boolean
---@param results System.Collections.Generic.List<T>
---@return void
function GameObject:GetComponentsInParent(includeInactive, results)
end

---@param includeInactive boolean
---@return T[]
function GameObject:GetComponentsInParent(includeInactive)
end

---@return T[]
function GameObject:GetComponentsInParent()
end

---@return booleanT
function GameObject:TryGetComponent(component)
end

---@param type System.Type
---@return booleanUnityEngine.Component
function GameObject:TryGetComponent(type, component)
end

---Retrieves the first active GameObject tagged with the specified tag. Returns null if no GameObject has the tag.
---@param tag string The tag to search for.
---@return UnityEngine.GameObject
function GameObject.FindWithTag(tag)
end

---@param tag string
---@param results System.Collections.Generic.List<UnityEngine.GameObject>
---@return void
function GameObject.FindGameObjectsWithTag(tag, results)
end

---@param methodName string
---@param options UnityEngine.SendMessageOptions
---@return void
function GameObject:SendMessageUpwards(methodName, options)
end

---@param methodName string
---@param options UnityEngine.SendMessageOptions
---@return void
function GameObject:SendMessage(methodName, options)
end

---@param methodName string
---@param options UnityEngine.SendMessageOptions
---@return void
function GameObject:BroadcastMessage(methodName, options)
end

---Adds a component of the specified type to the GameObject.
---@param componentType System.Type
---@return UnityEngine.Component
function GameObject:AddComponent(componentType)
end

---@return T
function GameObject:AddComponent()
end

---The number of components on the GameObject as an Integer value.
---@return integer
function GameObject:GetComponentCount()
end

---A reference to a component of type T at the specified index. If no component is found at the specified index, returns null.
---@param index integer The index position in the array of components at which to find the requested object.
---@return UnityEngine.Component
function GameObject:GetComponentAtIndex(index)
end

---@param index integer
---@return T
function GameObject:GetComponentAtIndex(index)
end

---The index of the specified Component if it exists. Otherwise, returns -1.
---@param component UnityEngine.Component The component to search for.
---@return integer
function GameObject:GetComponentIndex(component)
end

---Activates or deactivates the GameObject locally, according to the value of the supplied parameter.
---@param value boolean The active state to set, where true sets the GameObject to active and false sets it to inactive.
---@return void
function GameObject:SetActive(value)
end

---@param state boolean
---@return void
function GameObject:SetActiveRecursively(state)
end

---true if the GameObject has the given tag, false otherwise.
---@param tag string The tag to check for on the GameObject.
---@return boolean
function GameObject:CompareTag(tag)
end

---true if the GameObject has the given tag, false otherwise.
---@param tag UnityEngine.TagHandle A TagHandle representing the tag to check for on the GameObject.
---@return boolean
function GameObject:CompareTag(tag)
end

---@param tag string
---@return UnityEngine.GameObject
function GameObject.FindGameObjectWithTag(tag)
end

---Retrieves an array of all active GameObjects tagged with the specified tag. Returns an empty array if no GameObjects have the tag.
---@param tag string The name of the tag to search for GameObjects by.
---@return UnityEngine.GameObject[]
function GameObject.FindGameObjectsWithTag(tag)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject and on every ancestor of the behaviour.
---@param methodName string The name of the MonoBehaviour method to call.
---@param value table An optional parameter value to pass to the called method.
---@param options UnityEngine.SendMessageOptions Whether an error should be raised if the method doesn't exist on the target object.
---@return void
function GameObject:SendMessageUpwards(methodName, value, options)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject and on every ancestor of the behaviour.
---@param methodName string The name of the MonoBehaviour method to call.
---@param value table An optional parameter value to pass to the called method.
---@return void
function GameObject:SendMessageUpwards(methodName, value)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject and on every ancestor of the behaviour.
---@param methodName string The name of the MonoBehaviour method to call.
---@return void
function GameObject:SendMessageUpwards(methodName)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject.
---@param methodName string The name of the MonoBehaviour method to call.
---@param value table An optional parameter value to pass to the called method.
---@param options UnityEngine.SendMessageOptions Whether an error should be raised if the method doesn't exist on the target object.
---@return void
function GameObject:SendMessage(methodName, value, options)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject.
---@param methodName string The name of the MonoBehaviour method to call.
---@param value table An optional parameter value to pass to the called method.
---@return void
function GameObject:SendMessage(methodName, value)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject.
---@param methodName string The name of the MonoBehaviour method to call.
---@return void
function GameObject:SendMessage(methodName)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject or any of its children.
---@param methodName string The name of the MonoBehaviour method to call.
---@param parameter table An optional parameter value to pass to the called method.
---@param options UnityEngine.SendMessageOptions Whether an error should be raised if the method doesn't exist on the target object.
---@return void
function GameObject:BroadcastMessage(methodName, parameter, options)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject or any of its children.
---@param methodName string The name of the MonoBehaviour method to call.
---@param parameter table An optional parameter value to pass to the called method.
---@return void
function GameObject:BroadcastMessage(methodName, parameter)
end

---Calls the specified method on every MonoBehaviour attached to the GameObject or any of its children.
---@param methodName string The name of the MonoBehaviour method to call.
---@return void
function GameObject:BroadcastMessage(methodName)
end

---Finds and returns a GameObject with the specified name.
---@param name string The name of the GameObject to find.
---@return UnityEngine.GameObject
function GameObject.Find(name)
end

---@param instanceIDs Unity.Collections.NativeArray<int>
---@param active boolean
---@return void
function GameObject.SetGameObjectsActive(instanceIDs, active)
end

---@param instanceIDs System.ReadOnlySpan<int>
---@param active boolean
---@return void
function GameObject.SetGameObjectsActive(instanceIDs, active)
end

---@param sourceInstanceID integer
---@param count integer
---@param newInstanceIDs Unity.Collections.NativeArray<int>
---@param newTransformInstanceIDs Unity.Collections.NativeArray<int>
---@param destinationScene UnityEngine.SceneManagement.Scene
---@return void
function GameObject.InstantiateGameObjects(sourceInstanceID, count, newInstanceIDs, newTransformInstanceIDs, destinationScene)
end

---The Scene the GameObject with the specified instance ID is part of.
---@param instanceID integer The instance ID of the GameObject.
---@return UnityEngine.SceneManagement.Scene
function GameObject.GetScene(instanceID)
end

---@param clip UnityEngine.Object
---@param time number
---@return void
function GameObject:SampleAnimation(clip, time)
end

---Adds a component of the specified class name to the GameObject.
---@param className string
---@return UnityEngine.Component
function GameObject:AddComponent(className)
end

---@param animation UnityEngine.Object
---@return void
function GameObject:PlayAnimation(animation)
end

---@return void
function GameObject:StopAnimation()
end


---Base class for everything attached to a GameObject.
---@class UnityEngine.Component: UnityEngine.Object
---@overload fun(): UnityEngine.Component
local Component = {}
---The Transform attached to this GameObject.
---@type UnityEngine.Transform
Component.transform = nil

---The game object this component is attached to. A component is always attached to a game object.
---@type UnityEngine.GameObject
Component.gameObject = nil

---The tag of this game object.
---@type string
Component.tag = nil

---The Rigidbody attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.rigidbody = nil

---The Rigidbody2D that is attached to the Component's GameObject.
---@type UnityEngine.Component
Component.rigidbody2D = nil

---The Camera attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.camera = nil

---The Light attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.light = nil

---The Animation attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.animation = nil

---The ConstantForce attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.constantForce = nil

---The Renderer attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.renderer = nil

---The AudioSource attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.audio = nil

---The NetworkView attached to this GameObject (Read Only). (null if there is none attached).
---@type UnityEngine.Component
Component.networkView = nil

---The Collider attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.collider = nil

---The Collider2D component attached to the object.
---@type UnityEngine.Component
Component.collider2D = nil

---The HingeJoint attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.hingeJoint = nil

---The ParticleSystem attached to this GameObject. (Null if there is none attached).
---@type UnityEngine.Component
Component.particleSystem = nil

---A Component of the matching type, otherwise null if no Component is found.
---@param type System.Type The type of Component to retrieve.
---@return UnityEngine.Component
function Component:GetComponent(type)
end

---@return T
function Component:GetComponent()
end

---@param type System.Type
---@return booleanUnityEngine.Component
function Component:TryGetComponent(type, component)
end

---@return booleanT
function Component:TryGetComponent(component)
end

---A Component of the matching type, otherwise null if no Component is found.
---@param type string The name of the type of Component to get.
---@return UnityEngine.Component
function Component:GetComponent(type)
end

---A Component of the matching type, otherwise null if no Component is found.
---@param t System.Type The type of component to search for.
---@param includeInactive boolean Whether to include inactive child GameObjects in the search.
---@return UnityEngine.Component
function Component:GetComponentInChildren(t, includeInactive)
end

---A Component of the matching type, otherwise null if no Component is found.
---@param t System.Type The type of component to search for.
---@return UnityEngine.Component
function Component:GetComponentInChildren(t)
end

---@param includeInactive boolean
---@return T
function Component:GetComponentInChildren(includeInactive)
end

---@return T
function Component:GetComponentInChildren()
end

---An array of all found components matching the specified type.
---@param t System.Type The type of component to search for.
---@param includeInactive boolean Whether to include inactive child GameObjects in the search.
---@return UnityEngine.Component[]
function Component:GetComponentsInChildren(t, includeInactive)
end

---@param t System.Type
---@return UnityEngine.Component[]
function Component:GetComponentsInChildren(t)
end

---@param includeInactive boolean
---@return T[]
function Component:GetComponentsInChildren(includeInactive)
end

---@param includeInactive boolean
---@param result System.Collections.Generic.List<T>
---@return void
function Component:GetComponentsInChildren(includeInactive, result)
end

---@return T[]
function Component:GetComponentsInChildren()
end

---@param results System.Collections.Generic.List<T>
---@return void
function Component:GetComponentsInChildren(results)
end

---A Component of the matching type, otherwise null if no Component is found.
---@param t System.Type The type of component to search for.
---@param includeInactive boolean Whether to include inactive GameObjects in the search.
---@return UnityEngine.Component
function Component:GetComponentInParent(t, includeInactive)
end

---A Component of the matching type, otherwise null if no Component is found.
---@param t System.Type The type of component to search for.
---@return UnityEngine.Component
function Component:GetComponentInParent(t)
end

---@param includeInactive boolean
---@return T
function Component:GetComponentInParent(includeInactive)
end

---@return T
function Component:GetComponentInParent()
end

---An array of all found components matching the specified type.
---@param t System.Type The type of component to search for.
---@param includeInactive boolean Whether to include inactive GameObjects in the search.
---@return UnityEngine.Component[]
function Component:GetComponentsInParent(t, includeInactive)
end

---@param t System.Type
---@return UnityEngine.Component[]
function Component:GetComponentsInParent(t)
end

---@param includeInactive boolean
---@return T[]
function Component:GetComponentsInParent(includeInactive)
end

---@param includeInactive boolean
---@param results System.Collections.Generic.List<T>
---@return void
function Component:GetComponentsInParent(includeInactive, results)
end

---@return T[]
function Component:GetComponentsInParent()
end

---An array containing all matching components of type type.
---@param type System.Type The type of component to search for.
---@return UnityEngine.Component[]
function Component:GetComponents(type)
end

---@param type System.Type
---@param results System.Collections.Generic.List<UnityEngine.Component>
---@return void
function Component:GetComponents(type, results)
end

---@param results System.Collections.Generic.List<T>
---@return void
function Component:GetComponents(results)
end

---@return T[]
function Component:GetComponents()
end

---The component index.
---@return integer
function Component:GetComponentIndex()
end

---Returns true if GameObject has same tag. Returns false otherwise.
---@param tag string The tag to compare.
---@return boolean
function Component:CompareTag(tag)
end

---Returns true if GameObject has same tag. Returns false otherwise.
---@param tag UnityEngine.TagHandle A TagHandle representing the tag to compare.
---@return boolean
function Component:CompareTag(tag)
end

---Calls the method named methodName on every MonoBehaviour in this game object and on every ancestor of the behaviour.
---@param methodName string Name of method to call.
---@param value table Optional parameter value for the method.
---@param options UnityEngine.SendMessageOptions Should an error be raised if the method does not exist on the target object?
---@return void
function Component:SendMessageUpwards(methodName, value, options)
end

---Calls the method named methodName on every MonoBehaviour in this game object and on every ancestor of the behaviour.
---@param methodName string Name of method to call.
---@param value table Optional parameter value for the method.
---@return void
function Component:SendMessageUpwards(methodName, value)
end

---Calls the method named methodName on every MonoBehaviour in this game object and on every ancestor of the behaviour.
---@param methodName string Name of method to call.
---@return void
function Component:SendMessageUpwards(methodName)
end

---Calls the method named methodName on every MonoBehaviour in this game object and on every ancestor of the behaviour.
---@param methodName string Name of method to call.
---@param options UnityEngine.SendMessageOptions Should an error be raised if the method does not exist on the target object?
---@return void
function Component:SendMessageUpwards(methodName, options)
end

---Calls the method named methodName on every MonoBehaviour in this game object.
---@param methodName string Name of the method to call.
---@param value table Optional parameter for the method.
---@return void
function Component:SendMessage(methodName, value)
end

---Calls the method named methodName on every MonoBehaviour in this game object.
---@param methodName string Name of the method to call.
---@return void
function Component:SendMessage(methodName)
end

---Calls the method named methodName on every MonoBehaviour in this game object.
---@param methodName string Name of the method to call.
---@param value table Optional parameter for the method.
---@param options UnityEngine.SendMessageOptions Should an error be raised if the target object doesn't implement the method for the message?
---@return void
function Component:SendMessage(methodName, value, options)
end

---Calls the method named methodName on every MonoBehaviour in this game object.
---@param methodName string Name of the method to call.
---@param options UnityEngine.SendMessageOptions Should an error be raised if the target object doesn't implement the method for the message?
---@return void
function Component:SendMessage(methodName, options)
end

---Calls the method named methodName on every MonoBehaviour in this game object or any of its children.
---@param methodName string Name of the method to call.
---@param parameter table Optional parameter to pass to the method (can be any value).
---@param options UnityEngine.SendMessageOptions Should an error be raised if the method does not exist for a given target object?
---@return void
function Component:BroadcastMessage(methodName, parameter, options)
end

---Calls the method named methodName on every MonoBehaviour in this game object or any of its children.
---@param methodName string Name of the method to call.
---@param parameter table Optional parameter to pass to the method (can be any value).
---@return void
function Component:BroadcastMessage(methodName, parameter)
end

---Calls the method named methodName on every MonoBehaviour in this game object or any of its children.
---@param methodName string Name of the method to call.
---@return void
function Component:BroadcastMessage(methodName)
end

---Calls the method named methodName on every MonoBehaviour in this game object or any of its children.
---@param methodName string Name of the method to call.
---@param options UnityEngine.SendMessageOptions Should an error be raised if the method does not exist for a given target object?
---@return void
function Component:BroadcastMessage(methodName, options)
end


---Position, rotation and scale of an object.
---@class UnityEngine.Transform: UnityEngine.Component, System.Collections.IEnumerable
---@overload fun(): UnityEngine.Transform
local Transform = {}
---The world space position of the Transform.
---@type UnityEngine.Vector3
Transform.position = nil

---Position of the transform relative to the parent transform.
---@type UnityEngine.Vector3
Transform.localPosition = nil

---The rotation as Euler angles in degrees.
---@type UnityEngine.Vector3
Transform.eulerAngles = nil

---The rotation as Euler angles in degrees relative to the parent transform's rotation.
---@type UnityEngine.Vector3
Transform.localEulerAngles = nil

---The red axis of the transform in world space.
---@type UnityEngine.Vector3
Transform.right = nil

---The green axis of the transform in world space.
---@type UnityEngine.Vector3
Transform.up = nil

---Returns a normalized vector representing the blue axis of the transform in world space.
---@type UnityEngine.Vector3
Transform.forward = nil

---A Quaternion that stores the rotation of the Transform in world space.
---@type UnityEngine.Quaternion
Transform.rotation = nil

---The rotation of the transform relative to the transform rotation of the parent.
---@type UnityEngine.Quaternion
Transform.localRotation = nil

---The scale of the transform relative to the GameObjects parent.
---@type UnityEngine.Vector3
Transform.localScale = nil

---The parent of the transform.
---@type UnityEngine.Transform
Transform.parent = nil

---Matrix that transforms a point from world space into local space (Read Only).
---@type UnityEngine.Matrix4x4
Transform.worldToLocalMatrix = nil

---Matrix that transforms a point from local space into world space (Read Only).
---@type UnityEngine.Matrix4x4
Transform.localToWorldMatrix = nil

---Returns the topmost transform in the hierarchy.
---@type UnityEngine.Transform
Transform.root = nil

---The number of children the parent Transform has.
---@type integer
Transform.childCount = nil

---The global scale of the object (Read Only).
---@type UnityEngine.Vector3
Transform.lossyScale = nil

---Has the transform changed since the last time the flag was set to 'false'?
---@type boolean
Transform.hasChanged = nil

---The transform capacity of the transform's hierarchy data structure.
---@type integer
Transform.hierarchyCapacity = nil

---The number of transforms in the transform's hierarchy data structure.
---@type integer
Transform.hierarchyCount = nil

---Set the parent of the transform.
---@param p UnityEngine.Transform
---@return void
function Transform:SetParent(p)
end

---Set the parent of the transform.
---@param parent UnityEngine.Transform The parent Transform to use.
---@param worldPositionStays boolean If true, the parent-relative position, scale and rotation are modified such that the object keeps the same world space position, rotation and scale as before.
---@return void
function Transform:SetParent(parent, worldPositionStays)
end

---Sets the world space position and rotation of the Transform component.
---@param position UnityEngine.Vector3 The world space position to apply to the transform.
---@param rotation UnityEngine.Quaternion The world space rotation to apply to the transform.
---@return void
function Transform:SetPositionAndRotation(position, rotation)
end

---Sets the position and rotation of the Transform component in local space (i.e. relative to its parent transform).
---@param localPosition UnityEngine.Vector3 The local space position to apply to the transform.
---@param localRotation UnityEngine.Quaternion The local space rotation to apply to the transform.
---@return void
function Transform:SetLocalPositionAndRotation(localPosition, localRotation)
end

---@return voidUnityEngine.Vector3, UnityEngine.Quaternion
function Transform:GetPositionAndRotation(position, rotation)
end

---@return voidUnityEngine.Vector3, UnityEngine.Quaternion
function Transform:GetLocalPositionAndRotation(localPosition, localRotation)
end

---Moves the transform along its x, y, and z axes by the values of the translation parameter's x, y, and z components respectively.
---@param translation UnityEngine.Vector3 The amount by which to move the Transform.
---@param relativeTo UnityEngine.Space The coordinate system in which to apply the translation.
---@return void
function Transform:Translate(translation, relativeTo)
end

---Moves the transform along its x, y, and z axes by the values of the translation parameter's x, y, and z components respectively.
---@param translation UnityEngine.Vector3 The amount by which to move the Transform.
---@return void
function Transform:Translate(translation)
end

---Moves the transform by x along the x axis, y along the y axis, and z along the z axis.
---@param x number The amount by which to move the Transform on the x-axis.
---@param y number The amount by which to move the Transform on the y-axis.
---@param z number The amount by which to move the Transform on the z-axis.
---@param relativeTo UnityEngine.Space The coordinate system in which the translation is applied.
---@return void
function Transform:Translate(x, y, z, relativeTo)
end

---Moves the transform by x along the x axis, y along the y axis, and z along the z axis.
---@param x number The amount by which to move the Transform on the x-axis.
---@param y number The amount by which to move the Transform on the y-axis.
---@param z number The amount by which to move the Transform on the z-axis.
---@return void
function Transform:Translate(x, y, z)
end

---Moves the transform along its x, y, and z axes by the values of the translation parameter's x, y, and z components respectively.
---@param translation UnityEngine.Vector3 The amount by which to move the Transform.
---@param relativeTo UnityEngine.Transform Defines the coordinate system used for the translation.
---@return void
function Transform:Translate(translation, relativeTo)
end

---Moves the transform by x along the x axis, y along the y axis, and z along the z axis.
---@param x number The amount by which to move the Transform on the x-axis.
---@param y number The amount by which to move the Transform on the y-axis.
---@param z number The amount by which to move the Transform on the z-axis.
---@param relativeTo UnityEngine.Transform Defines the coordinate system used for the translation.
---@return void
function Transform:Translate(x, y, z, relativeTo)
end

---Applies a rotation of eulerAngles.z degrees around the z-axis, eulerAngles.x degrees around the x-axis, and eulerAngles.y degrees around the y-axis (in that order).
---@param eulers UnityEngine.Vector3 The rotation to apply in euler angles.
---@param relativeTo UnityEngine.Space Determines whether to rotate the GameObject either locally to  the GameObject or relative to the Scene in world space.
---@return void
function Transform:Rotate(eulers, relativeTo)
end

---Applies a rotation of eulerAngles.z degrees around the z-axis, eulerAngles.x degrees around the x-axis, and eulerAngles.y degrees around the y-axis (in that order).
---@param eulers UnityEngine.Vector3 The rotation to apply in euler angles.
---@return void
function Transform:Rotate(eulers)
end

---The implementation of this method applies a rotation of zAngle degrees around the z axis, xAngle degrees around the x axis, and yAngle degrees around the y axis (in that order).
---@param xAngle number Degrees to rotate the GameObject around the X axis.
---@param yAngle number Degrees to rotate the GameObject around the Y axis.
---@param zAngle number Degrees to rotate the GameObject around the Z axis.
---@param relativeTo UnityEngine.Space Determines whether to rotate the GameObject either locally to the GameObject or relative to the Scene in world space.
---@return void
function Transform:Rotate(xAngle, yAngle, zAngle, relativeTo)
end

---The implementation of this method applies a rotation of zAngle degrees around the z axis, xAngle degrees around the x axis, and yAngle degrees around the y axis (in that order).
---@param xAngle number Degrees to rotate the GameObject around the X axis.
---@param yAngle number Degrees to rotate the GameObject around the Y axis.
---@param zAngle number Degrees to rotate the GameObject around the Z axis.
---@return void
function Transform:Rotate(xAngle, yAngle, zAngle)
end

---Rotates the object around the given axis by the number of degrees defined by the given angle.
---@param axis UnityEngine.Vector3 The axis to apply rotation to.
---@param angle number The degrees of rotation to apply.
---@param relativeTo UnityEngine.Space Determines whether to rotate the GameObject either locally to the GameObject or relative to the Scene in world space.
---@return void
function Transform:Rotate(axis, angle, relativeTo)
end

---Rotates the object around the given axis by the number of degrees defined by the given angle.
---@param axis UnityEngine.Vector3 The axis to apply rotation to.
---@param angle number The degrees of rotation to apply.
---@return void
function Transform:Rotate(axis, angle)
end

---Rotates the transform about axis passing through point in world coordinates by angle degrees.
---@param point UnityEngine.Vector3 The world-space point to rotate the target object around.
---@param axis UnityEngine.Vector3 The world-space axis to rotate the target object around. This vector does not need to be unit length.
---@param angle number The angle to rotate, provided in degrees.
---@return void
function Transform:RotateAround(point, axis, angle)
end

---Rotates the transform so the forward vector points at target's current position.
---@param target UnityEngine.Transform Object to point towards.
---@param worldUp UnityEngine.Vector3 Vector specifying the upward direction.
---@return void
function Transform:LookAt(target, worldUp)
end

---Rotates the transform so the forward vector points at target's current position.
---@param target UnityEngine.Transform Object to point towards.
---@return void
function Transform:LookAt(target)
end

---Rotates the transform so the forward vector points at worldPosition.
---@param worldPosition UnityEngine.Vector3 Point to look at.
---@param worldUp UnityEngine.Vector3 Vector specifying the upward direction.
---@return void
function Transform:LookAt(worldPosition, worldUp)
end

---Rotates the transform so the forward vector points at worldPosition.
---@param worldPosition UnityEngine.Vector3 Point to look at.
---@return void
function Transform:LookAt(worldPosition)
end

---Transforms direction from local space to world space.
---@param direction UnityEngine.Vector3
---@return UnityEngine.Vector3
function Transform:TransformDirection(direction)
end

---Transforms direction x, y, z from local space to world space.
---@param x number
---@param y number
---@param z number
---@return UnityEngine.Vector3
function Transform:TransformDirection(x, y, z)
end

---@param directions System.ReadOnlySpan<UnityEngine.Vector3>
---@param transformedDirections System.Span<UnityEngine.Vector3>
---@return void
function Transform:TransformDirections(directions, transformedDirections)
end

---@param directions System.Span<UnityEngine.Vector3>
---@return void
function Transform:TransformDirections(directions)
end

---Transforms a direction from world space to local space. The opposite of Transform.TransformDirection.
---@param direction UnityEngine.Vector3
---@return UnityEngine.Vector3
function Transform:InverseTransformDirection(direction)
end

---Transforms the direction x, y, z from world space to local space. The opposite of Transform.TransformDirection.
---@param x number
---@param y number
---@param z number
---@return UnityEngine.Vector3
function Transform:InverseTransformDirection(x, y, z)
end

---@param directions System.ReadOnlySpan<UnityEngine.Vector3>
---@param transformedDirections System.Span<UnityEngine.Vector3>
---@return void
function Transform:InverseTransformDirections(directions, transformedDirections)
end

---@param directions System.Span<UnityEngine.Vector3>
---@return void
function Transform:InverseTransformDirections(directions)
end

---The transformed vector, in world space.
---@param vector UnityEngine.Vector3 The vector to transform, in local space.
---@return UnityEngine.Vector3
function Transform:TransformVector(vector)
end

---The transformed vector, in world space.
---@param x number The x component of the vector to transform, in local space.
---@param y number The y component of the vector to transform, in local space.
---@param z number The z component of the vector to transform, in local space.
---@return UnityEngine.Vector3
function Transform:TransformVector(x, y, z)
end

---@param vectors System.ReadOnlySpan<UnityEngine.Vector3>
---@param transformedVectors System.Span<UnityEngine.Vector3>
---@return void
function Transform:TransformVectors(vectors, transformedVectors)
end

---@param vectors System.Span<UnityEngine.Vector3>
---@return void
function Transform:TransformVectors(vectors)
end

---The transformed vector, in local space.
---@param vector UnityEngine.Vector3 The vector to transform, in world space.
---@return UnityEngine.Vector3
function Transform:InverseTransformVector(vector)
end

---The transformed vector, in local space.
---@param x number The x component of the vector to transform, in world space.
---@param y number The y component of the vector to transform, in world space.
---@param z number The z component of the vector to transform, in world space.
---@return UnityEngine.Vector3
function Transform:InverseTransformVector(x, y, z)
end

---@param vectors System.ReadOnlySpan<UnityEngine.Vector3>
---@param transformedVectors System.Span<UnityEngine.Vector3>
---@return void
function Transform:InverseTransformVectors(vectors, transformedVectors)
end

---@param vectors System.Span<UnityEngine.Vector3>
---@return void
function Transform:InverseTransformVectors(vectors)
end

---Transforms position from local space to world space.
---@param position UnityEngine.Vector3
---@return UnityEngine.Vector3
function Transform:TransformPoint(position)
end

---Transforms the position x, y, z from local space to world space.
---@param x number
---@param y number
---@param z number
---@return UnityEngine.Vector3
function Transform:TransformPoint(x, y, z)
end

---@param positions System.ReadOnlySpan<UnityEngine.Vector3>
---@param transformedPositions System.Span<UnityEngine.Vector3>
---@return void
function Transform:TransformPoints(positions, transformedPositions)
end

---@param positions System.Span<UnityEngine.Vector3>
---@return void
function Transform:TransformPoints(positions)
end

---Transforms position from world space to local space.
---@param position UnityEngine.Vector3
---@return UnityEngine.Vector3
function Transform:InverseTransformPoint(position)
end

---Transforms the position x, y, z from world space to local space.
---@param x number
---@param y number
---@param z number
---@return UnityEngine.Vector3
function Transform:InverseTransformPoint(x, y, z)
end

---@param positions System.ReadOnlySpan<UnityEngine.Vector3>
---@param transformedPositions System.Span<UnityEngine.Vector3>
---@return void
function Transform:InverseTransformPoints(positions, transformedPositions)
end

---@param positions System.Span<UnityEngine.Vector3>
---@return void
function Transform:InverseTransformPoints(positions)
end

---Unparents all of the target object's children.
---@return void
function Transform:DetachChildren()
end

---Move the transform to the start of the local transform list.
---@return void
function Transform:SetAsFirstSibling()
end

---Move the transform to the end of the local transform list.
---@return void
function Transform:SetAsLastSibling()
end

---Sets the sibling index.
---@param index integer Index to set.
---@return void
function Transform:SetSiblingIndex(index)
end

---The index of this Transform, relative to its siblings.
---@return integer
function Transform:GetSiblingIndex()
end

---The found child transform. Null if child with matching name isn't found.
---@param n string The search string, either the name of an immediate child or a hierarchy path for finding a descendent.
---@return UnityEngine.Transform
function Transform:Find(n)
end

---Is this transform a child of parent?
---@param parent UnityEngine.Transform
---@return boolean
function Transform:IsChildOf(parent)
end

---@param n string
---@return UnityEngine.Transform
function Transform:FindChild(n)
end

---@return System.Collections.IEnumerator
function Transform:GetEnumerator()
end

---@param axis UnityEngine.Vector3
---@param angle number
---@return void
function Transform:RotateAround(axis, angle)
end

---@param axis UnityEngine.Vector3
---@param angle number
---@return void
function Transform:RotateAroundLocal(axis, angle)
end

---Transform child by index.
---@param index integer Index of the child transform to return. Must be smaller than Transform.childCount.
---@return UnityEngine.Transform
function Transform:GetChild(index)
end

---@return integer
function Transform:GetChildCount()
end


---MonoBehaviour is a base class that many Unity scripts derive from.
---@class UnityEngine.MonoBehaviour: UnityEngine.Behaviour
---@overload fun(): UnityEngine.MonoBehaviour
local MonoBehaviour = {}
---Cancellation token raised when the MonoBehaviour is destroyed (Read Only).
---@type System.Threading.CancellationToken
MonoBehaviour.destroyCancellationToken = nil

---Disabling this lets you skip the GUI layout phase.
---@type boolean
MonoBehaviour.useGUILayout = nil

---Returns a boolean value which represents if Start was called.
---@type boolean
MonoBehaviour.didStart = nil

---Returns a boolean value which represents if Awake was called.
---@type boolean
MonoBehaviour.didAwake = nil

---Allow a specific instance of a MonoBehaviour to run in edit mode (only available in the editor).
---@type boolean
MonoBehaviour.runInEditMode = nil

---Is any invoke pending on this MonoBehaviour?
---@return boolean
function MonoBehaviour:IsInvoking()
end

---Cancels all Invoke calls on this MonoBehaviour.
---@return void
function MonoBehaviour:CancelInvoke()
end

---Invokes the method methodName in time seconds.
---@param methodName string
---@param time number
---@return void
function MonoBehaviour:Invoke(methodName, time)
end

---Invokes the method methodName in time seconds, then repeatedly every repeatRate seconds.
---@param methodName string The name of a method to invoke.
---@param time number Start invoking after n seconds.
---@param repeatRate number Repeat every n seconds.
---@return void
function MonoBehaviour:InvokeRepeating(methodName, time, repeatRate)
end

---Cancels all Invoke calls with name methodName on this behaviour.
---@param methodName string
---@return void
function MonoBehaviour:CancelInvoke(methodName)
end

---Is any invoke on methodName pending?
---@param methodName string
---@return boolean
function MonoBehaviour:IsInvoking(methodName)
end

---Starts a coroutine named methodName.
---@param methodName string
---@return UnityEngine.Coroutine
function MonoBehaviour:StartCoroutine(methodName)
end

---Starts a coroutine named methodName.
---@param methodName string
---@param value table
---@return UnityEngine.Coroutine
function MonoBehaviour:StartCoroutine(methodName, value)
end

---Starts a coroutine.
---@param routine System.Collections.IEnumerator
---@return UnityEngine.Coroutine
function MonoBehaviour:StartCoroutine(routine)
end

---@param routine System.Collections.IEnumerator
---@return UnityEngine.Coroutine
function MonoBehaviour:StartCoroutine_Auto(routine)
end

---Stops the first coroutine named methodName, or the coroutine stored in routine running on this behaviour.
---@param routine System.Collections.IEnumerator Name of the function in code, including coroutines.
---@return void
function MonoBehaviour:StopCoroutine(routine)
end

---Stops the first coroutine named methodName, or the coroutine stored in routine running on this behaviour.
---@param routine UnityEngine.Coroutine Name of the function in code, including coroutines.
---@return void
function MonoBehaviour:StopCoroutine(routine)
end

---Stops the first coroutine named methodName, or the coroutine stored in routine running on this behaviour.
---@param methodName string Name of coroutine.
---@return void
function MonoBehaviour:StopCoroutine(methodName)
end

---Stops all coroutines running on this MonoBehaviour.
---@return void
function MonoBehaviour:StopAllCoroutines()
end

---Logs a message to the Unity Console. Functionally equivalent to Debug.Log.
---@param message table The message to display in the console.
---@return void
function MonoBehaviour.print(message)
end


---Representation of 2D vectors and points.
---@class UnityEngine.Vector2: System.ValueType, System.IEquatable<UnityEngine.Vector2>, System.IFormattable
---@overload fun(): UnityEngine.Vector2
---@overload fun(x: number,y: number): UnityEngine.Vector2
local Vector2 = {}
---X component of the vector.
---@type number
Vector2.x = nil

---Y component of the vector.
---@type number
Vector2.y = nil

---@type number
Vector2.kEpsilon = nil

---@type number
Vector2.kEpsilonNormalSqrt = nil

---@type number
Vector2.this[] = nil

---Returns a normalized vector based on the current vector. The normalized vector has a magnitude of 1 and is in the same direction as the current vector. Returns a zero vector If the current vector is too small to be normalized.
---@type UnityEngine.Vector2
Vector2.normalized = nil

---Returns the length of this vector (Read Only).
---@type number
Vector2.magnitude = nil

---Returns the squared length of this vector (Read Only).
---@type number
Vector2.sqrMagnitude = nil

---Shorthand for writing Vector2(0, 0).
---@type UnityEngine.Vector2
Vector2.zero = nil

---Shorthand for writing Vector2(1, 1).
---@type UnityEngine.Vector2
Vector2.one = nil

---Shorthand for writing Vector2(0, 1).
---@type UnityEngine.Vector2
Vector2.up = nil

---Shorthand for writing Vector2(0, -1).
---@type UnityEngine.Vector2
Vector2.down = nil

---Shorthand for writing Vector2(-1, 0).
---@type UnityEngine.Vector2
Vector2.left = nil

---Shorthand for writing Vector2(1, 0).
---@type UnityEngine.Vector2
Vector2.right = nil

---Shorthand for writing Vector2(float.PositiveInfinity, float.PositiveInfinity).
---@type UnityEngine.Vector2
Vector2.positiveInfinity = nil

---Shorthand for writing Vector2(float.NegativeInfinity, float.NegativeInfinity).
---@type UnityEngine.Vector2
Vector2.negativeInfinity = nil

---Set x and y components of an existing Vector2.
---@param newX number
---@param newY number
---@return void
function Vector2:Set(newX, newY)
end

---Linearly interpolates between vectors a and b by t.
---@param a UnityEngine.Vector2
---@param b UnityEngine.Vector2
---@param t number
---@return UnityEngine.Vector2
function Vector2.Lerp(a, b, t)
end

---Linearly interpolates between vectors a and b by t.
---@param a UnityEngine.Vector2
---@param b UnityEngine.Vector2
---@param t number
---@return UnityEngine.Vector2
function Vector2.LerpUnclamped(a, b, t)
end

---Moves a point current towards target.
---@param current UnityEngine.Vector2
---@param target UnityEngine.Vector2
---@param maxDistanceDelta number
---@return UnityEngine.Vector2
function Vector2.MoveTowards(current, target, maxDistanceDelta)
end

---Multiplies two vectors component-wise.
---@param a UnityEngine.Vector2
---@param b UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.Scale(a, b)
end

---Multiplies every component of this vector by the same component of scale.
---@param scale UnityEngine.Vector2
---@return void
function Vector2:Scale(scale)
end

---Makes this vector have a magnitude of 1.
---@return void
function Vector2:Normalize()
end

---Returns a formatted string for this vector.
---@return string
function Vector2:ToString()
end

---Returns a formatted string for this vector.
---@param format string A numeric format string.
---@return string
function Vector2:ToString(format)
end

---Returns a formatted string for this vector.
---@param format string A numeric format string.
---@param formatProvider System.IFormatProvider An object that specifies culture-specific formatting.
---@return string
function Vector2:ToString(format, formatProvider)
end

---@return integer
function Vector2:GetHashCode()
end

---Returns true if the given vector is exactly equal to this vector.
---@param other table
---@return boolean
function Vector2:Equals(other)
end

---@param other UnityEngine.Vector2
---@return boolean
function Vector2:Equals(other)
end

---Reflects a vector off the surface defined by a normal.
---@param inDirection UnityEngine.Vector2 The direction vector towards the surface.
---@param inNormal UnityEngine.Vector2 The normal vector that defines the surface.
---@return UnityEngine.Vector2
function Vector2.Reflect(inDirection, inNormal)
end

---The perpendicular direction.
---@param inDirection UnityEngine.Vector2 The input direction.
---@return UnityEngine.Vector2
function Vector2.Perpendicular(inDirection)
end

---Dot Product of two vectors.
---@param lhs UnityEngine.Vector2
---@param rhs UnityEngine.Vector2
---@return number
function Vector2.Dot(lhs, rhs)
end

---The unsigned angle in degrees between the two vectors.
---@param from UnityEngine.Vector2 The vector from which the angular difference is measured.
---@param to UnityEngine.Vector2 The vector to which the angular difference is measured.
---@return number
function Vector2.Angle(from, to)
end

---The signed angle in degrees between the two vectors.
---@param from UnityEngine.Vector2 The vector from which the angular difference is measured.
---@param to UnityEngine.Vector2 The vector to which the angular difference is measured.
---@return number
function Vector2.SignedAngle(from, to)
end

---The distance between the two positions, as an absolute (positive) value.
---@param a UnityEngine.Vector2 First position to compare distance from.
---@param b UnityEngine.Vector2 Second position to compare distance from.
---@return number
function Vector2.Distance(a, b)
end

---Returns a copy of vector with its magnitude clamped to maxLength.
---@param vector UnityEngine.Vector2
---@param maxLength number
---@return UnityEngine.Vector2
function Vector2.ClampMagnitude(vector, maxLength)
end

---@param a UnityEngine.Vector2
---@return number
function Vector2.SqrMagnitude(a)
end

---@return number
function Vector2:SqrMagnitude()
end

---Returns a vector that is made from the smallest components of two vectors.
---@param lhs UnityEngine.Vector2
---@param rhs UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.Min(lhs, rhs)
end

---Returns a vector that is made from the largest components of two vectors.
---@param lhs UnityEngine.Vector2
---@param rhs UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.Max(lhs, rhs)
end

---@param current UnityEngine.Vector2
---@param target UnityEngine.Vector2
---@param currentVelocity UnityEngine.Vector2
---@param smoothTime number
---@param maxSpeed number
---@return UnityEngine.Vector2UnityEngine.Vector2
function Vector2.SmoothDamp(current, target, currentVelocity, smoothTime, maxSpeed)
end

---@param current UnityEngine.Vector2
---@param target UnityEngine.Vector2
---@param currentVelocity UnityEngine.Vector2
---@param smoothTime number
---@return UnityEngine.Vector2UnityEngine.Vector2
function Vector2.SmoothDamp(current, target, currentVelocity, smoothTime)
end

---@param current UnityEngine.Vector2
---@param target UnityEngine.Vector2
---@param currentVelocity UnityEngine.Vector2
---@param smoothTime number
---@param maxSpeed number
---@param deltaTime number
---@return UnityEngine.Vector2UnityEngine.Vector2
function Vector2.SmoothDamp(current, target, currentVelocity, smoothTime, maxSpeed, deltaTime)
end

---@param a UnityEngine.Vector2
---@param b UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.op_Addition(a, b)
end

---@param a UnityEngine.Vector2
---@param b UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.op_Subtraction(a, b)
end

---@param a UnityEngine.Vector2
---@param b UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.op_Multiply(a, b)
end

---@param a UnityEngine.Vector2
---@param b UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.op_Division(a, b)
end

---@param a UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.op_UnaryNegation(a)
end

---@param a UnityEngine.Vector2
---@param d number
---@return UnityEngine.Vector2
function Vector2.op_Multiply(a, d)
end

---@param d number
---@param a UnityEngine.Vector2
---@return UnityEngine.Vector2
function Vector2.op_Multiply(d, a)
end

---@param a UnityEngine.Vector2
---@param d number
---@return UnityEngine.Vector2
function Vector2.op_Division(a, d)
end

---@param lhs UnityEngine.Vector2
---@param rhs UnityEngine.Vector2
---@return boolean
function Vector2.op_Equality(lhs, rhs)
end

---@param lhs UnityEngine.Vector2
---@param rhs UnityEngine.Vector2
---@return boolean
function Vector2.op_Inequality(lhs, rhs)
end

---@param v UnityEngine.Vector3
---@return UnityEngine.Vector2
function Vector2.op_Implicit(v)
end

---@param v UnityEngine.Vector2
---@return UnityEngine.Vector3
function Vector2.op_Implicit(v)
end


---Representation of 3D vectors and points.
---@class UnityEngine.Vector3: System.ValueType, System.IEquatable<UnityEngine.Vector3>, System.IFormattable
---@overload fun(): UnityEngine.Vector3
---@overload fun(x: number,y: number,z: number): UnityEngine.Vector3
---@overload fun(x: number,y: number): UnityEngine.Vector3
local Vector3 = {}
---@type number
Vector3.kEpsilon = nil

---@type number
Vector3.kEpsilonNormalSqrt = nil

---X component of the vector.
---@type number
Vector3.x = nil

---Y component of the vector.
---@type number
Vector3.y = nil

---Z component of the vector.
---@type number
Vector3.z = nil

---@type number
Vector3.this[] = nil

---Returns a normalized vector based on the current vector. The normalized vector has a magnitude of 1 and is in the same direction as the current vector. Returns a zero vector If the current vector is too small to be normalized.
---@type UnityEngine.Vector3
Vector3.normalized = nil

---Returns the length of this vector (Read Only).
---@type number
Vector3.magnitude = nil

---Returns the squared length of this vector (Read Only).
---@type number
Vector3.sqrMagnitude = nil

---Shorthand for writing Vector3(0, 0, 0).
---@type UnityEngine.Vector3
Vector3.zero = nil

---Shorthand for writing Vector3(1, 1, 1).
---@type UnityEngine.Vector3
Vector3.one = nil

---Shorthand for writing Vector3(0, 0, 1).
---@type UnityEngine.Vector3
Vector3.forward = nil

---Shorthand for writing Vector3(0, 0, -1).
---@type UnityEngine.Vector3
Vector3.back = nil

---Shorthand for writing Vector3(0, 1, 0).
---@type UnityEngine.Vector3
Vector3.up = nil

---Shorthand for writing Vector3(0, -1, 0).
---@type UnityEngine.Vector3
Vector3.down = nil

---Shorthand for writing Vector3(-1, 0, 0).
---@type UnityEngine.Vector3
Vector3.left = nil

---Shorthand for writing Vector3(1, 0, 0).
---@type UnityEngine.Vector3
Vector3.right = nil

---Shorthand for writing Vector3(float.PositiveInfinity, float.PositiveInfinity, float.PositiveInfinity).
---@type UnityEngine.Vector3
Vector3.positiveInfinity = nil

---Shorthand for writing Vector3(float.NegativeInfinity, float.NegativeInfinity, float.NegativeInfinity).
---@type UnityEngine.Vector3
Vector3.negativeInfinity = nil

---@type UnityEngine.Vector3
Vector3.fwd = nil

---The resulting spherically interpolated Vector3 direction.
---@param a UnityEngine.Vector3 The first Vector3 direction to interpolate between.
---@param b UnityEngine.Vector3 The second Vector3 direction to interpolate between.
---@param t number The interpolation parameter with an expected value in the range [0,1].
---@return UnityEngine.Vector3
function Vector3.Slerp(a, b, t)
end

---Spherically interpolates between two vectors.
---@param a UnityEngine.Vector3
---@param b UnityEngine.Vector3
---@param t number
---@return UnityEngine.Vector3
function Vector3.SlerpUnclamped(a, b, t)
end

---@param normal UnityEngine.Vector3
---@param tangent UnityEngine.Vector3
---@return voidUnityEngine.Vector3, UnityEngine.Vector3
function Vector3.OrthoNormalize(normal, tangent)
end

---@param normal UnityEngine.Vector3
---@param tangent UnityEngine.Vector3
---@param binormal UnityEngine.Vector3
---@return voidUnityEngine.Vector3, UnityEngine.Vector3, UnityEngine.Vector3
function Vector3.OrthoNormalize(normal, tangent, binormal)
end

---The location that RotateTowards generates.
---@param current UnityEngine.Vector3 The vector being managed.
---@param target UnityEngine.Vector3 The vector.
---@param maxRadiansDelta number The maximum angle in radians allowed for this rotation.
---@param maxMagnitudeDelta number The maximum allowed change in vector magnitude for this rotation.
---@return UnityEngine.Vector3
function Vector3.RotateTowards(current, target, maxRadiansDelta, maxMagnitudeDelta)
end

---Interpolated value, equals to a + (b - a) * t.
---@param a UnityEngine.Vector3 Start value, returned when t = 0.
---@param b UnityEngine.Vector3 End value, returned when t = 1.
---@param t number Value used to interpolate between a and b.
---@return UnityEngine.Vector3
function Vector3.Lerp(a, b, t)
end

---Linearly interpolates between two vectors.
---@param a UnityEngine.Vector3
---@param b UnityEngine.Vector3
---@param t number
---@return UnityEngine.Vector3
function Vector3.LerpUnclamped(a, b, t)
end

---The new position.
---@param current UnityEngine.Vector3 The position to move from.
---@param target UnityEngine.Vector3 The position to move towards.
---@param maxDistanceDelta number Distance to move current per call.
---@return UnityEngine.Vector3
function Vector3.MoveTowards(current, target, maxDistanceDelta)
end

---@param current UnityEngine.Vector3
---@param target UnityEngine.Vector3
---@param currentVelocity UnityEngine.Vector3
---@param smoothTime number
---@param maxSpeed number
---@return UnityEngine.Vector3UnityEngine.Vector3
function Vector3.SmoothDamp(current, target, currentVelocity, smoothTime, maxSpeed)
end

---@param current UnityEngine.Vector3
---@param target UnityEngine.Vector3
---@param currentVelocity UnityEngine.Vector3
---@param smoothTime number
---@return UnityEngine.Vector3UnityEngine.Vector3
function Vector3.SmoothDamp(current, target, currentVelocity, smoothTime)
end

---@param current UnityEngine.Vector3
---@param target UnityEngine.Vector3
---@param currentVelocity UnityEngine.Vector3
---@param smoothTime number
---@param maxSpeed number
---@param deltaTime number
---@return UnityEngine.Vector3UnityEngine.Vector3
function Vector3.SmoothDamp(current, target, currentVelocity, smoothTime, maxSpeed, deltaTime)
end

---Set x, y and z components of an existing Vector3.
---@param newX number X component value.
---@param newY number Y component value.
---@param newZ number Z component value.
---@return void
function Vector3:Set(newX, newY, newZ)
end

---Multiplies two vectors component-wise.
---@param a UnityEngine.Vector3
---@param b UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.Scale(a, b)
end

---Multiplies every component of this vector by the same component of scale.
---@param scale UnityEngine.Vector3
---@return void
function Vector3:Scale(scale)
end

---The cross product of the lhs and rhs vectors. This vector is usually not normalized.
---@param lhs UnityEngine.Vector3 The first input vector.
---@param rhs UnityEngine.Vector3 The second input vector.
---@return UnityEngine.Vector3
function Vector3.Cross(lhs, rhs)
end

---@return integer
function Vector3:GetHashCode()
end

---Returns true if the given vector is exactly equal to this vector.
---@param other table
---@return boolean
function Vector3:Equals(other)
end

---@param other UnityEngine.Vector3
---@return boolean
function Vector3:Equals(other)
end

---Reflects a vector off the plane defined by a normal.
---@param inDirection UnityEngine.Vector3 The direction vector towards the plane.
---@param inNormal UnityEngine.Vector3 The normal vector that defines the plane.
---@return UnityEngine.Vector3
function Vector3.Reflect(inDirection, inNormal)
end

---A new vector with the same direction as the original vector but with a magnitude of 1.0.
---@param value UnityEngine.Vector3 The vector to be normalized.
---@return UnityEngine.Vector3
function Vector3.Normalize(value)
end

---Makes this vector have a magnitude of 1.
---@return void
function Vector3:Normalize()
end

---The dot product of the lhs and rhs vectors.
---@param lhs UnityEngine.Vector3 The left operand of the dot product.
---@param rhs UnityEngine.Vector3 The right operand of the dot product.
---@return number
function Vector3.Dot(lhs, rhs)
end

---Projects a vector onto another vector.
---@param vector UnityEngine.Vector3
---@param onNormal UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.Project(vector, onNormal)
end

---The orthogonal projection of vector on the plane.
---@param vector UnityEngine.Vector3 The vector to project on the plane.
---@param planeNormal UnityEngine.Vector3 The normal which defines the plane to project on.
---@return UnityEngine.Vector3
function Vector3.ProjectOnPlane(vector, planeNormal)
end

---The angle in degrees between the two vectors.
---@param from UnityEngine.Vector3 The vector from which the angular difference is measured.
---@param to UnityEngine.Vector3 The vector to which the angular difference is measured.
---@return number
function Vector3.Angle(from, to)
end

---Returns the signed angle between from and to in degrees.
---@param from UnityEngine.Vector3 The vector from which the angular difference is measured.
---@param to UnityEngine.Vector3 The vector to which the angular difference is measured.
---@param axis UnityEngine.Vector3 A vector around which the other vectors are rotated.
---@return number
function Vector3.SignedAngle(from, to, axis)
end

---The scalar distance between points a and b.
---@param a UnityEngine.Vector3 The first three-dimensional point as a Vector3.
---@param b UnityEngine.Vector3 The second three-dimensional point as a Vector3.
---@return number
function Vector3.Distance(a, b)
end

---Returns a copy of vector with its magnitude clamped to maxLength.
---@param vector UnityEngine.Vector3
---@param maxLength number
---@return UnityEngine.Vector3
function Vector3.ClampMagnitude(vector, maxLength)
end

---@param vector UnityEngine.Vector3
---@return number
function Vector3.Magnitude(vector)
end

---@param vector UnityEngine.Vector3
---@return number
function Vector3.SqrMagnitude(vector)
end

---Returns a vector that is made from the smallest components of two vectors.
---@param lhs UnityEngine.Vector3
---@param rhs UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.Min(lhs, rhs)
end

---Returns a vector that is made from the largest components of two vectors.
---@param lhs UnityEngine.Vector3
---@param rhs UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.Max(lhs, rhs)
end

---@param a UnityEngine.Vector3
---@param b UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.op_Addition(a, b)
end

---@param a UnityEngine.Vector3
---@param b UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.op_Subtraction(a, b)
end

---@param a UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.op_UnaryNegation(a)
end

---@param a UnityEngine.Vector3
---@param d number
---@return UnityEngine.Vector3
function Vector3.op_Multiply(a, d)
end

---@param d number
---@param a UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.op_Multiply(d, a)
end

---@param a UnityEngine.Vector3
---@param d number
---@return UnityEngine.Vector3
function Vector3.op_Division(a, d)
end

---@param lhs UnityEngine.Vector3
---@param rhs UnityEngine.Vector3
---@return boolean
function Vector3.op_Equality(lhs, rhs)
end

---@param lhs UnityEngine.Vector3
---@param rhs UnityEngine.Vector3
---@return boolean
function Vector3.op_Inequality(lhs, rhs)
end

---Returns a formatted string for this vector.
---@return string
function Vector3:ToString()
end

---Returns a formatted string for this vector.
---@param format string A numeric format string.
---@return string
function Vector3:ToString(format)
end

---Returns a formatted string for this vector.
---@param format string A numeric format string.
---@param formatProvider System.IFormatProvider An object that specifies culture-specific formatting.
---@return string
function Vector3:ToString(format, formatProvider)
end

---@param from UnityEngine.Vector3
---@param to UnityEngine.Vector3
---@return number
function Vector3.AngleBetween(from, to)
end

---@param excludeThis UnityEngine.Vector3
---@param fromThat UnityEngine.Vector3
---@return UnityEngine.Vector3
function Vector3.Exclude(excludeThis, fromThat)
end


---Quaternions are used to represent rotations.
---@class UnityEngine.Quaternion: System.ValueType, System.IEquatable<UnityEngine.Quaternion>, System.IFormattable
---@overload fun(): UnityEngine.Quaternion
---@overload fun(x: number,y: number,z: number,w: number): UnityEngine.Quaternion
local Quaternion = {}
---X component of the Quaternion. Don't modify this directly unless you know quaternions inside out.
---@type number
Quaternion.x = nil

---Y component of the Quaternion. Don't modify this directly unless you know quaternions inside out.
---@type number
Quaternion.y = nil

---Z component of the Quaternion. Don't modify this directly unless you know quaternions inside out.
---@type number
Quaternion.z = nil

---W component of the Quaternion. Do not directly modify quaternions.
---@type number
Quaternion.w = nil

---@type number
Quaternion.kEpsilon = nil

---@type number
Quaternion.this[] = nil

---The identity rotation (Read Only).
---@type UnityEngine.Quaternion
Quaternion.identity = nil

---Returns or sets the euler angle representation of the rotation in degrees.
---@type UnityEngine.Vector3
Quaternion.eulerAngles = nil

---Returns this quaternion with a magnitude of 1 (Read Only).
---@type UnityEngine.Quaternion
Quaternion.normalized = nil

---A unit quaternion which rotates from fromDirection to toDirection.
---@param fromDirection UnityEngine.Vector3 A non-unit or unit vector representing a direction axis to rotate.
---@param toDirection UnityEngine.Vector3 A non-unit or unit vector representing the target direction axis.
---@return UnityEngine.Quaternion
function Quaternion.FromToRotation(fromDirection, toDirection)
end

---Returns the Inverse of rotation.
---@param rotation UnityEngine.Quaternion
---@return UnityEngine.Quaternion
function Quaternion.Inverse(rotation)
end

---A unit quaternion spherically interpolated between quaternions a and b.
---@param a UnityEngine.Quaternion Start unit quaternion value, returned when t = 0.
---@param b UnityEngine.Quaternion End unit quaternion value, returned when t = 1.
---@param t number Interpolation ratio. Value is clamped to the range [0, 1].
---@return UnityEngine.Quaternion
function Quaternion.Slerp(a, b, t)
end

---A unit quaternion spherically interpolated between unit quaternions a and b.
---@param a UnityEngine.Quaternion Start unit quaternion value, returned when t = 0.
---@param b UnityEngine.Quaternion End unit quaternion value, returned when t = 1.
---@param t number Interpolation ratio. Value is unclamped.
---@return UnityEngine.Quaternion
function Quaternion.SlerpUnclamped(a, b, t)
end

---A unit quaternion interpolated between quaternions a and b.
---@param a UnityEngine.Quaternion Start unit quaternion value, returned when t = 0.
---@param b UnityEngine.Quaternion End unit quaternion value, returned when t = 1.
---@param t number Interpolation ratio. The value is clamped to the range [0, 1].
---@return UnityEngine.Quaternion
function Quaternion.Lerp(a, b, t)
end

---Interpolates between a and b by t and normalizes the result afterwards. The parameter t is not clamped.
---@param a UnityEngine.Quaternion
---@param b UnityEngine.Quaternion
---@param t number
---@return UnityEngine.Quaternion
function Quaternion.LerpUnclamped(a, b, t)
end

---Creates a rotation which rotates angle degrees around axis.
---@param angle number
---@param axis UnityEngine.Vector3
---@return UnityEngine.Quaternion
function Quaternion.AngleAxis(angle, axis)
end

---Creates a rotation with the specified forward and upwards directions.
---@param forward UnityEngine.Vector3 The direction to look in.
---@param upwards UnityEngine.Vector3 The vector that defines in which direction up is.
---@return UnityEngine.Quaternion
function Quaternion.LookRotation(forward, upwards)
end

---Creates a rotation with the specified forward and upwards directions.
---@param forward UnityEngine.Vector3 The direction to look in.
---@return UnityEngine.Quaternion
function Quaternion.LookRotation(forward)
end

---Set x, y, z and w components of an existing Quaternion.
---@param newX number
---@param newY number
---@param newZ number
---@param newW number
---@return void
function Quaternion:Set(newX, newY, newZ, newW)
end

---@param lhs UnityEngine.Quaternion
---@param rhs UnityEngine.Quaternion
---@return UnityEngine.Quaternion
function Quaternion.op_Multiply(lhs, rhs)
end

---@param rotation UnityEngine.Quaternion
---@param point UnityEngine.Vector3
---@return UnityEngine.Vector3
function Quaternion.op_Multiply(rotation, point)
end

---@param lhs UnityEngine.Quaternion
---@param rhs UnityEngine.Quaternion
---@return boolean
function Quaternion.op_Equality(lhs, rhs)
end

---@param lhs UnityEngine.Quaternion
---@param rhs UnityEngine.Quaternion
---@return boolean
function Quaternion.op_Inequality(lhs, rhs)
end

---The dot product between two rotations.
---@param a UnityEngine.Quaternion
---@param b UnityEngine.Quaternion
---@return number
function Quaternion.Dot(a, b)
end

---Creates a rotation with the specified forward and upwards directions.
---@param view UnityEngine.Vector3 The direction to look in.
---@return void
function Quaternion:SetLookRotation(view)
end

---Creates a rotation with the specified forward and upwards directions.
---@param view UnityEngine.Vector3 The direction to look in.
---@param up UnityEngine.Vector3 The vector that defines in which direction up is.
---@return void
function Quaternion:SetLookRotation(view, up)
end

---Returns the angle in degrees between two rotations a and b. The resulting angle ranges from 0 to 180.
---@param a UnityEngine.Quaternion
---@param b UnityEngine.Quaternion
---@return number
function Quaternion.Angle(a, b)
end

---The Euler angle rotation specified by the angles x,y,z converted to a Quaternion. The rotation order is ZXY.
---@param x number Rotation in degrees around the x-axis.
---@param y number Rotation in degrees around the y-axis.
---@param z number Rotation in degrees around the z-axis.
---@return UnityEngine.Quaternion
function Quaternion.Euler(x, y, z)
end

---The Euler angle rotation specified by the angles euler.x,euler.y,euler.z converted to a Quaternion. The rotation order is ZXY.
---@param euler UnityEngine.Vector3 The x,y,z vector components of euler represent the input Euler angle rotations in degrees around the corresponding axes.
---@return UnityEngine.Quaternion
function Quaternion.Euler(euler)
end

---@return voidfloat, UnityEngine.Vector3
function Quaternion:ToAngleAxis(angle, axis)
end

---Creates a rotation which rotates from fromDirection to toDirection.
---@param fromDirection UnityEngine.Vector3
---@param toDirection UnityEngine.Vector3
---@return void
function Quaternion:SetFromToRotation(fromDirection, toDirection)
end

---A unit quaternion rotated towards to by an angular step of maxDegreesDelta.
---@param from UnityEngine.Quaternion The unit quaternion to be aligned with to.
---@param to UnityEngine.Quaternion The target unit quaternion.
---@param maxDegreesDelta number The maximum angle in degrees allowed for this rotation.
---@return UnityEngine.Quaternion
function Quaternion.RotateTowards(from, to, maxDegreesDelta)
end

---Converts this quaternion to a quaternion with the same orientation but with a magnitude of 1.0.
---@param q UnityEngine.Quaternion
---@return UnityEngine.Quaternion
function Quaternion.Normalize(q)
end

---@return void
function Quaternion:Normalize()
end

---@return integer
function Quaternion:GetHashCode()
end

---@param other table
---@return boolean
function Quaternion:Equals(other)
end

---@param other UnityEngine.Quaternion
---@return boolean
function Quaternion:Equals(other)
end

---Returns a formatted string for this quaternion.
---@return string
function Quaternion:ToString()
end

---Returns a formatted string for this quaternion.
---@param format string A numeric format string.
---@return string
function Quaternion:ToString(format)
end

---Returns a formatted string for this quaternion.
---@param format string A numeric format string.
---@param formatProvider System.IFormatProvider An object that specifies culture-specific formatting.
---@return string
function Quaternion:ToString(format, formatProvider)
end

---@param x number
---@param y number
---@param z number
---@return UnityEngine.Quaternion
function Quaternion.EulerRotation(x, y, z)
end

---@param euler UnityEngine.Vector3
---@return UnityEngine.Quaternion
function Quaternion.EulerRotation(euler)
end

---@param x number
---@param y number
---@param z number
---@return void
function Quaternion:SetEulerRotation(x, y, z)
end

---@param euler UnityEngine.Vector3
---@return void
function Quaternion:SetEulerRotation(euler)
end

---@return UnityEngine.Vector3
function Quaternion:ToEuler()
end

---@param x number
---@param y number
---@param z number
---@return UnityEngine.Quaternion
function Quaternion.EulerAngles(x, y, z)
end

---@param euler UnityEngine.Vector3
---@return UnityEngine.Quaternion
function Quaternion.EulerAngles(euler)
end

---@return voidUnityEngine.Vector3, float
function Quaternion:ToAxisAngle(axis, angle)
end

---@param x number
---@param y number
---@param z number
---@return void
function Quaternion:SetEulerAngles(x, y, z)
end

---@param euler UnityEngine.Vector3
---@return void
function Quaternion:SetEulerAngles(euler)
end

---@param rotation UnityEngine.Quaternion
---@return UnityEngine.Vector3
function Quaternion.ToEulerAngles(rotation)
end

---@return UnityEngine.Vector3
function Quaternion:ToEulerAngles()
end

---@param axis UnityEngine.Vector3
---@param angle number
---@return void
function Quaternion:SetAxisAngle(axis, angle)
end

---@param axis UnityEngine.Vector3
---@param angle number
---@return UnityEngine.Quaternion
function Quaternion.AxisAngle(axis, angle)
end


---Provides an interface to get time information from Unity.
---@class UnityEngine.Time: object
---@overload fun(): UnityEngine.Time
local Time = {}
---The time at the beginning of the current frame in seconds since the start of the application (Read Only).
---@type number
Time.time = nil

---The double precision time at the beginning of this frame (Read Only). This is the time in seconds since the start of the game.
---@type number
Time.timeAsDouble = nil

---The time this frame has started (Read Only). This is the time in seconds since the start of the game represented as a RationalTime.
---@type Unity.IntegerTime.RationalTime
Time.timeAsRational = nil

---The time in seconds since the last non-additive scene finished loading (Read Only).
---@type number
Time.timeSinceLevelLoad = nil

---The double precision time in seconds since the last non-additive scene finished loading (Read Only).
---@type number
Time.timeSinceLevelLoadAsDouble = nil

---The interval in seconds from the last frame to the current one (Read Only).
---@type number
Time.deltaTime = nil

---The time at which the current MonoBehaviour.FixedUpdate started in seconds since the start of the game (Read Only).
---@type number
Time.fixedTime = nil

---The double precision time since the last MonoBehaviour.FixedUpdate started (Read Only). This is the time in seconds since the start of the game.
---@type number
Time.fixedTimeAsDouble = nil

---The timeScale-independent time for this frame (Read Only). This is the time in seconds since the start of the game.
---@type number
Time.unscaledTime = nil

---The double precision timeScale-independent time for this frame (Read Only). This is the time in seconds since the start of the game.
---@type number
Time.unscaledTimeAsDouble = nil

---The timeScale-independent time at the beginning of the last MonoBehaviour.FixedUpdate phase (Read Only). This is the time in seconds since the start of the game.
---@type number
Time.fixedUnscaledTime = nil

---The double precision timeScale-independent time at the beginning of the last MonoBehaviour.FixedUpdate (Read Only). This is the time in seconds since the start of the game.
---@type number
Time.fixedUnscaledTimeAsDouble = nil

---The timeScale-independent interval in seconds from the last frame to the current one (Read Only).
---@type number
Time.unscaledDeltaTime = nil

---The interval in seconds of timeScale-independent ("real") time at which physics and other fixed frame rate updates (like MonoBehaviour's MonoBehaviour.FixedUpdate) are performed.(Read Only).
---@type number
Time.fixedUnscaledDeltaTime = nil

---The interval in seconds of in-game time at which physics and other fixed frame rate updates (like MonoBehaviour's MonoBehaviour.FixedUpdate) are performed.
---@type number
Time.fixedDeltaTime = nil

---The maximum value of Time.deltaTime in any given frame. This is a time in seconds that limits the increase of Time.time between two frames.
---@type number
Time.maximumDeltaTime = nil

---A smoothed out Time.deltaTime (Read Only).
---@type number
Time.smoothDeltaTime = nil

---The maximum time a frame can spend on particle updates. If the frame takes longer than this, then updates are split into multiple smaller updates.
---@type number
Time.maximumParticleDeltaTime = nil

---The scale at which time passes.
---@type number
Time.timeScale = nil

---The total number of frames since the start of the game (Read Only).
---@type integer
Time.frameCount = nil

---@type integer
Time.renderedFrameCount = nil

---The real time in seconds since the game started (Read Only).
---@type number
Time.realtimeSinceStartup = nil

---The real time in seconds since the game started (Read Only). Double precision version of Time.realtimeSinceStartup.
---@type number
Time.realtimeSinceStartupAsDouble = nil

---Slows your application’s playback time to allow Unity to save screenshots in between frames.
---@type number
Time.captureDeltaTime = nil

---Slows your application’s playback time to allow Unity to save screenshots in between frames.
---@type Unity.IntegerTime.RationalTime
Time.captureDeltaTimeRational = nil

---The reciprocal of Time.captureDeltaTime.
---@type integer
Time.captureFramerate = nil

---Returns true if called inside a fixed time step callback (like MonoBehaviour's MonoBehaviour.FixedUpdate), otherwise returns false (Read Only).
---@type boolean
Time.inFixedTimeStep = nil


---Class containing methods to ease debugging while developing a game.
---@class UnityEngine.Debug: object
---@overload fun(): UnityEngine.Debug
local Debug = {}
---Get default debug logger.
---@type UnityEngine.ILogger
Debug.unityLogger = nil

---Allows you to enable or disable the developer console.
---@type boolean
Debug.developerConsoleEnabled = nil

---Controls whether the development console is visible.
---@type boolean
Debug.developerConsoleVisible = nil

---In the Build Settings dialog there is a check box called "Development Build".
---@type boolean
Debug.isDebugBuild = nil

---@type UnityEngine.ILogger
Debug.logger = nil

---Draws a line between specified start and end points.
---@param start UnityEngine.Vector3 Point in world space where the line should start.
---@param end UnityEngine.Vector3 Point in world space where the line should end.
---@param color UnityEngine.Color Color of the line.
---@param duration number How long the line should be visible for.
---@return void
function Debug.DrawLine(start, _end, color, duration)
end

---Draws a line between specified start and end points.
---@param start UnityEngine.Vector3 Point in world space where the line should start.
---@param end UnityEngine.Vector3 Point in world space where the line should end.
---@param color UnityEngine.Color Color of the line.
---@return void
function Debug.DrawLine(start, _end, color)
end

---Draws a line between specified start and end points.
---@param start UnityEngine.Vector3 Point in world space where the line should start.
---@param end UnityEngine.Vector3 Point in world space where the line should end.
---@return void
function Debug.DrawLine(start, _end)
end

---Draws a line between specified start and end points.
---@param start UnityEngine.Vector3 Point in world space where the line should start.
---@param end UnityEngine.Vector3 Point in world space where the line should end.
---@param color UnityEngine.Color Color of the line.
---@param duration number How long the line should be visible for.
---@param depthTest boolean Determines whether objects closer to the camera obscure the line.
---@return void
function Debug.DrawLine(start, _end, color, duration, depthTest)
end

---Draws a line from start to start + dir in world coordinates.
---@param start UnityEngine.Vector3 Point in world space where the ray should start.
---@param dir UnityEngine.Vector3 Direction and length of the ray.
---@param color UnityEngine.Color Color of the drawn line.
---@param duration number How long the line will be visible for (in seconds).
---@return void
function Debug.DrawRay(start, dir, color, duration)
end

---Draws a line from start to start + dir in world coordinates.
---@param start UnityEngine.Vector3 Point in world space where the ray should start.
---@param dir UnityEngine.Vector3 Direction and length of the ray.
---@param color UnityEngine.Color Color of the drawn line.
---@return void
function Debug.DrawRay(start, dir, color)
end

---Draws a line from start to start + dir in world coordinates.
---@param start UnityEngine.Vector3 Point in world space where the ray should start.
---@param dir UnityEngine.Vector3 Direction and length of the ray.
---@return void
function Debug.DrawRay(start, dir)
end

---Draws a line from start to start + dir in world coordinates.
---@param start UnityEngine.Vector3 Point in world space where the ray should start.
---@param dir UnityEngine.Vector3 Direction and length of the ray.
---@param color UnityEngine.Color Color of the drawn line.
---@param duration number How long the line will be visible for (in seconds).
---@param depthTest boolean Determines whether objects closer to the camera obscure the line.
---@return void
function Debug.DrawRay(start, dir, color, duration, depthTest)
end

---Pauses the editor.
---@return void
function Debug.Break()
end

---@return void
function Debug.DebugBreak()
end

---Populate an unmanaged buffer with the current managed call stack as a sequence of UTF-8 bytes, without allocating GC memory. Returns the number of bytes written into the buffer.
---@param buffer byte* Target buffer to receive the callstack text
---@param bufferMax integer Max number of bytes to write
---@param projectFolder string Project folder path, to clean up path names
---@return integer
function Debug.ExtractStackTraceNoAlloc(buffer, bufferMax, projectFolder)
end

---Logs a message to the Unity Console.
---@param message table String or object to be converted to string representation for display.
---@return void
function Debug.Log(message)
end

---Logs a message to the Unity Console.
---@param message table String or object to be converted to string representation for display.
---@param context UnityEngine.Object Object to which the message applies.
---@return void
function Debug.Log(message, context)
end

---Logs a formatted message to the Unity Console.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogFormat(format, args)
end

---Logs a formatted message to the Unity Console.
---@param context UnityEngine.Object Object to which the message applies.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogFormat(context, format, args)
end

---Logs a formatted message to the Unity Console.
---@param logType UnityEngine.LogType Type of message e.g. warn or error etc.
---@param logOptions UnityEngine.LogOption Option flags to treat the log message special.
---@param context UnityEngine.Object Object to which the message applies.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogFormat(logType, logOptions, context, format, args)
end

---A variant of Debug.Log that logs an error message to the console.
---@param message table String or object to be converted to string representation for display.
---@return void
function Debug.LogError(message)
end

---A variant of Debug.Log that logs an error message to the console.
---@param message table String or object to be converted to string representation for display.
---@param context UnityEngine.Object Object to which the message applies.
---@return void
function Debug.LogError(message, context)
end

---Logs a formatted error message to the Unity console.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogErrorFormat(format, args)
end

---Logs a formatted error message to the Unity console.
---@param context UnityEngine.Object Object to which the message applies.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogErrorFormat(context, format, args)
end

---Clears errors from the developer console.
---@return void
function Debug.ClearDeveloperConsole()
end

---A variant of Debug.Log that logs an error message to the console.
---@param exception System.Exception Runtime Exception.
---@return void
function Debug.LogException(exception)
end

---A variant of Debug.Log that logs an error message to the console.
---@param exception System.Exception Runtime Exception.
---@param context UnityEngine.Object Object to which the message applies.
---@return void
function Debug.LogException(exception, context)
end

---A variant of Debug.Log that logs a warning message to the console.
---@param message table String or object to be converted to string representation for display.
---@return void
function Debug.LogWarning(message)
end

---A variant of Debug.Log that logs a warning message to the console.
---@param message table String or object to be converted to string representation for display.
---@param context UnityEngine.Object Object to which the message applies.
---@return void
function Debug.LogWarning(message, context)
end

---Logs a formatted warning message to the Unity Console.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogWarningFormat(format, args)
end

---Logs a formatted warning message to the Unity Console.
---@param context UnityEngine.Object Object to which the message applies.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogWarningFormat(context, format, args)
end

---Assert a condition and logs an error message to the Unity console on failure.
---@param condition boolean Condition you expect to be true.
---@return void
function Debug.Assert(condition)
end

---Assert a condition and logs an error message to the Unity console on failure.
---@param condition boolean Condition you expect to be true.
---@param context UnityEngine.Object Object to which the message applies.
---@return void
function Debug.Assert(condition, context)
end

---Assert a condition and logs an error message to the Unity console on failure.
---@param condition boolean Condition you expect to be true.
---@param message table String or object to be converted to string representation for display.
---@return void
function Debug.Assert(condition, message)
end

---@param condition boolean
---@param message string
---@return void
function Debug.Assert(condition, message)
end

---Assert a condition and logs an error message to the Unity console on failure.
---@param condition boolean Condition you expect to be true.
---@param message table String or object to be converted to string representation for display.
---@param context UnityEngine.Object Object to which the message applies.
---@return void
function Debug.Assert(condition, message, context)
end

---@param condition boolean
---@param message string
---@param context UnityEngine.Object
---@return void
function Debug.Assert(condition, message, context)
end

---Assert a condition and logs a formatted error message to the Unity console on failure.
---@param condition boolean Condition you expect to be true.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.AssertFormat(condition, format, args)
end

---Assert a condition and logs a formatted error message to the Unity console on failure.
---@param condition boolean Condition you expect to be true.
---@param context UnityEngine.Object Object to which the message applies.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.AssertFormat(condition, context, format, args)
end

---A variant of Debug.Log that logs an assertion message to the console.
---@param message table String or object to be converted to string representation for display.
---@return void
function Debug.LogAssertion(message)
end

---A variant of Debug.Log that logs an assertion message to the console.
---@param message table String or object to be converted to string representation for display.
---@param context UnityEngine.Object Object to which the message applies.
---@return void
function Debug.LogAssertion(message, context)
end

---Logs a formatted assertion message to the Unity console.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogAssertionFormat(format, args)
end

---Logs a formatted assertion message to the Unity console.
---@param context UnityEngine.Object Object to which the message applies.
---@param format string A composite format string.
---@param args object[] Format arguments.
---@return void
function Debug.LogAssertionFormat(context, format, args)
end

---Returns any captured startup logs
---@return UnityEngine.Debug.StartupLog[]
function Debug.RetrieveStartupLogs()
end

---Performs an integrity check of the currently running process and return discovered errors.
---@param level UnityEngine.IntegrityCheckLevel Thoroughness of integrity check.
---@return string
function Debug.CheckIntegrity(level)
end

---Returns whether a given validation level is currently enabled.
---@param level UnityEngine.ValidationLevel The validation level to test.
---@return boolean
function Debug.IsValidationLevelEnabled(level)
end

---@param condition boolean
---@param format string
---@param args object[]
---@return void
function Debug.Assert(condition, format, args)
end


---Provides access to application runtime data.
---@class UnityEngine.Application: object
---@overload fun(): UnityEngine.Application
local Application = {}
---Is some level being loaded? (Read Only) (Obsolete).
---@type boolean
Application.isLoadingLevel = nil

---How many bytes have we downloaded from the main unity web stream (Read Only).
---@type integer
Application.streamedBytes = nil

---Indicates whether Unity's Web Player security model is enabled.
---@type boolean
Application.webSecurityEnabled = nil

---Returns true when called in any kind of built Player, or when called in the Editor in Play mode (Read Only).
---@type boolean
Application.isPlaying = nil

---Whether the Player currently has focus (Read Only).
---@type boolean
Application.isFocused = nil

---Returns a GUID for this build (Read Only).
---@type string
Application.buildGUID = nil

---Determines whether the Player should run when the application is in the background
---@type boolean
Application.runInBackground = nil

---Returns true when Unity is launched with the -batchmode flag from the command line (Read Only).
---@type boolean
Application.isBatchMode = nil

---Contains the path to the game data folder on the target device (Read Only).
---@type string
Application.dataPath = nil

---The path to the StreamingAssets  folder (Read Only).
---@type string
Application.streamingAssetsPath = nil

---Contains the path to a persistent data directory (Read-only).
---@type string
Application.persistentDataPath = nil

---Contains the path to a temporary data / cache directory (Read Only).
---@type string
Application.temporaryCachePath = nil

---The URL of the document. For WebGL, this is a web URL. For Android, iOS, or Universal Windows Platform (UWP) this is a deep link URL (Read Only).
---@type string
Application.absoluteURL = nil

---The version of the Unity runtime used to play the content.
---@type string
Application.unityVersion = nil

---Returns application version number (Read Only).
---@type string
Application.version = nil

---Returns the name of the store or package that installed the application (Read Only).
---@type string
Application.installerName = nil

---Returns the application identifier at runtime.
---@type string
Application.identifier = nil

---Returns application install mode (Read Only).
---@type UnityEngine.ApplicationInstallMode
Application.installMode = nil

---Returns application running in a sandbox environment (Read-only).
---@type UnityEngine.ApplicationSandboxType
Application.sandboxType = nil

---Returns application product name (Read Only).
---@type string
Application.productName = nil

---Returns application company name (Read Only).
---@type string
Application.companyName = nil

---A unique cloud project identifier. It is unique for every project (Read Only).
---@type string
Application.cloudProjectId = nil

---Specifies the target frame rate at which Unity tries to render your game.
---@type integer
Application.targetFrameRate = nil

---Obsolete. Use Application.SetStackTraceLogType.
---@type UnityEngine.StackTraceLogType
Application.stackTraceLogType = nil

---Returns the path to the console log file, or an empty string if the current platform does not support log files.
---@type string
Application.consoleLogPath = nil

---Priority of background loading thread.
---@type UnityEngine.ThreadPriority
Application.backgroundLoadingPriority = nil

---Returns false if application is altered in any way after it was built.
---@type boolean
Application.genuine = nil

---Returns true if application integrity can be confirmed.
---@type boolean
Application.genuineCheckAvailable = nil

---Checks whether splash screen is being shown.
---@type boolean
Application.isShowingSplashScreen = nil

---Returns the platform the game is running on (Read Only).
---@type UnityEngine.RuntimePlatform
Application.platform = nil

---Identifies whether the current Runtime platform is a known mobile platform.
---@type boolean
Application.isMobilePlatform = nil

---Is the current Runtime platform a known console platform.
---@type boolean
Application.isConsolePlatform = nil

---The language in which the user's operating system is running in.
---@type UnityEngine.SystemLanguage
Application.systemLanguage = nil

---Returns the type of internet reachability currently possible on the device.
---@type UnityEngine.NetworkReachability
Application.internetReachability = nil

---@type boolean
Application.isPlayer = nil

---Cancellation token raised on exiting Play mode (Editor) or on quitting the application (Read Only).
---@type System.Threading.CancellationToken
Application.exitCancellationToken = nil

---The total number of levels available (Read Only).
---@type integer
Application.levelCount = nil

---Note: This is now obsolete. Use SceneManager.GetActiveScene instead. (Read Only).
---@type integer
Application.loadedLevel = nil

---The name of the level that was last loaded (Read Only).
---@type string
Application.loadedLevelName = nil

---Whether the game is running inside the Unity Editor (Read Only).
---@type boolean
Application.isEditor = nil

---@param exitCode integer
---@return void
function Application.Quit(exitCode)
end

---Quits the player application.
---@return void
function Application.Quit()
end

---Cancels quitting the application. This is used for showing a splash screen at the end of a game.
---@return void
function Application.CancelQuit()
end

---Unloads the Unity Player.
---@return void
function Application.Unload()
end

---How far has the download progressed? [0...1].
---@param levelIndex integer
---@return number
function Application.GetStreamProgressForLevel(levelIndex)
end

---How far has the download progressed? [0...1].
---@param levelName string
---@return number
function Application.GetStreamProgressForLevel(levelName)
end

---Checks if the streamed level can be loaded.
---@param levelIndex integer
---@return boolean
function Application.CanStreamedLevelBeLoaded(levelIndex)
end

---Checks if the streamed level can be loaded.
---@param levelName string
---@return boolean
function Application.CanStreamedLevelBeLoaded(levelName)
end

---True if the object is part of the playing world.
---@param obj UnityEngine.Object The object to test.
---@return boolean
function Application.IsPlaying(obj)
end

---Returns an array of feature tags in use for this build.
---@return string[]
function Application.GetBuildTags()
end

---Set an array of feature tags for this build.
---@param buildTags string[]
---@return void
function Application.SetBuildTags(buildTags)
end

---Is Unity activated with the Pro license?
---@return boolean
function Application.HasProLicense()
end

---Execution of a script function in the contained web page.
---@param script string The Javascript function to call.
---@return void
function Application.ExternalEval(script)
end

---@param delegateMethod UnityEngine.Application.AdvertisingIdentifierCallback
---@return boolean
function Application.RequestAdvertisingIdentifierAsync(delegateMethod)
end

---Opens the URL specified, subject to the permissions and limitations of your app’s current platform and environment.
---@param url string The URL to open.
---@return void
function Application.OpenURL(url)
end

---@param mode integer
---@return void
function Application.ForceCrash(mode)
end

---Get stack trace logging options. The default value is StackTraceLogType.ScriptOnly.
---@param logType UnityEngine.LogType
---@return UnityEngine.StackTraceLogType
function Application.GetStackTraceLogType(logType)
end

---Set stack trace logging options. The default value is StackTraceLogType.ScriptOnly.
---@param logType UnityEngine.LogType
---@param stackTraceType UnityEngine.StackTraceLogType
---@return void
function Application.SetStackTraceLogType(logType, stackTraceType)
end

---Request authorization to use the webcam or microphone on iOS, and the webcam only on WebGL.
---@param mode UnityEngine.UserAuthorization
---@return UnityEngine.AsyncOperation
function Application.RequestUserAuthorization(mode)
end

---Check if the user has authorized use of the webcam or microphone on iOS and WebGL.
---@param mode UnityEngine.UserAuthorization
---@return boolean
function Application.HasUserAuthorization(mode)
end

---@param value UnityEngine.Application.LowMemoryCallback
---@return void
function Application.add_lowMemory(value)
end

---@param value UnityEngine.Application.LowMemoryCallback
---@return void
function Application.remove_lowMemory(value)
end

---@param value UnityEngine.Application.MemoryUsageChangedCallback
---@return void
function Application.add_memoryUsageChanged(value)
end

---@param value UnityEngine.Application.MemoryUsageChangedCallback
---@return void
function Application.remove_memoryUsageChanged(value)
end

---@param value UnityEngine.Application.LogCallback
---@return void
function Application.add_logMessageReceived(value)
end

---@param value UnityEngine.Application.LogCallback
---@return void
function Application.remove_logMessageReceived(value)
end

---@param value UnityEngine.Application.LogCallback
---@return void
function Application.add_logMessageReceivedThreaded(value)
end

---@param value UnityEngine.Application.LogCallback
---@return void
function Application.remove_logMessageReceivedThreaded(value)
end

---Calls a function in the web page that contains the WebGL Player.
---@param functionName string Name of the function to call.
---@param args object[] Array of arguments passed in the call.
---@return void
function Application.ExternalCall(functionName, args)
end

---@param o UnityEngine.Object
---@return void
function Application.DontDestroyOnLoad(o)
end

---Captures a screenshot at path filename as a PNG file.
---@param filename string Pathname to save the screenshot file to.
---@param superSize integer Factor by which to increase resolution.
---@return void
function Application.CaptureScreenshot(filename, superSize)
end

---Captures a screenshot at path filename as a PNG file.
---@param filename string Pathname to save the screenshot file to.
---@return void
function Application.CaptureScreenshot(filename)
end

---@param value UnityEngine.Events.UnityAction
---@return void
function Application.add_onBeforeRender(value)
end

---@param value UnityEngine.Events.UnityAction
---@return void
function Application.remove_onBeforeRender(value)
end

---@param value System.Action<bool>
---@return void
function Application.add_focusChanged(value)
end

---@param value System.Action<bool>
---@return void
function Application.remove_focusChanged(value)
end

---@param value System.Action<string>
---@return void
function Application.add_deepLinkActivated(value)
end

---@param value System.Action<string>
---@return void
function Application.remove_deepLinkActivated(value)
end

---@param value System.Func<bool>
---@return void
function Application.add_wantsToQuit(value)
end

---@param value System.Func<bool>
---@return void
function Application.remove_wantsToQuit(value)
end

---@param value System.Action
---@return void
function Application.add_quitting(value)
end

---@param value System.Action
---@return void
function Application.remove_quitting(value)
end

---@param value System.Action
---@return void
function Application.add_unloading(value)
end

---@param value System.Action
---@return void
function Application.remove_unloading(value)
end

---@param handler UnityEngine.Application.LogCallback
---@return void
function Application.RegisterLogCallback(handler)
end

---@param handler UnityEngine.Application.LogCallback
---@return void
function Application.RegisterLogCallbackThreaded(handler)
end

---Note: This is now obsolete. Use SceneManager.LoadScene instead.
---@param index integer The level to load.
---@return void
function Application.LoadLevel(index)
end

---Note: This is now obsolete. Use SceneManager.LoadScene instead.
---@param name string The name of the level to load.
---@return void
function Application.LoadLevel(name)
end

---Loads a level additively.
---@param index integer
---@return void
function Application.LoadLevelAdditive(index)
end

---Loads a level additively.
---@param name string
---@return void
function Application.LoadLevelAdditive(name)
end

---Loads the level asynchronously in the background.
---@param index integer
---@return UnityEngine.AsyncOperation
function Application.LoadLevelAsync(index)
end

---Loads the level asynchronously in the background.
---@param levelName string
---@return UnityEngine.AsyncOperation
function Application.LoadLevelAsync(levelName)
end

---Loads the level additively and asynchronously in the background.
---@param index integer
---@return UnityEngine.AsyncOperation
function Application.LoadLevelAdditiveAsync(index)
end

---Loads the level additively and asynchronously in the background.
---@param levelName string
---@return UnityEngine.AsyncOperation
function Application.LoadLevelAdditiveAsync(levelName)
end

---Returns true, if the Scene is unloaded.
---@param index integer Index of the Scene in the PlayerSettings to unload.
---@return boolean
function Application.UnloadLevel(index)
end

---Returns true, if the Scene is unloaded.
---@param scenePath string Name of the Scene to Unload.
---@return boolean
function Application.UnloadLevel(scenePath)
end


---Provides access to display information.
---@class UnityEngine.Screen: object
---@overload fun(): UnityEngine.Screen
local Screen = {}
---The current width of the screen window in pixels (Read Only).
---@type integer
Screen.width = nil

---The current height of the screen window in pixels (Read Only).
---@type integer
Screen.height = nil

---The current pixel density of the screen measured in dots-per-inch (DPI) (Read Only).
---@type number
Screen.dpi = nil

---The current screen resolution (Read Only).
---@type UnityEngine.Resolution
Screen.currentResolution = nil

---Returns all full-screen resolutions that the monitor supports (Read Only).
---@type UnityEngine.Resolution[]
Screen.resolutions = nil

---Get the requested MSAA sample count of the screen buffer.
---@type integer
Screen.msaaSamples = nil

---Enables full-screen mode for the application.
---@type boolean
Screen.fullScreen = nil

---Set this property to one of the values in FullScreenMode to change the display mode of your application.
---@type UnityEngine.FullScreenMode
Screen.fullScreenMode = nil

---Returns the safe area of the screen in pixels (Read Only).
---@type UnityEngine.Rect
Screen.safeArea = nil

---Returns a list of screen areas that are not functional for displaying content (Read Only).
---@type UnityEngine.Rect[]
Screen.cutouts = nil

---Enables auto-rotation to portrait.
---@type boolean
Screen.autorotateToPortrait = nil

---Enables auto-rotation to portrait, upside down.
---@type boolean
Screen.autorotateToPortraitUpsideDown = nil

---Enables auto-rotation to landscape left.
---@type boolean
Screen.autorotateToLandscapeLeft = nil

---Enables auto-rotation to landscape right.
---@type boolean
Screen.autorotateToLandscapeRight = nil

---Specifies logical orientation of the screen.
---@type UnityEngine.ScreenOrientation
Screen.orientation = nil

---A power saving setting, allowing the screen to dim some time after the last active user interaction.
---@type integer
Screen.sleepTimeout = nil

---Indicates the current brightness of the screen.
---@type number
Screen.brightness = nil

---The position of the top left corner of the main window relative to the top left corner of the display.
---@type UnityEngine.Vector2Int
Screen.mainWindowPosition = nil

---The display information associated with the display that the main application window is on.
---@type UnityEngine.DisplayInfo
Screen.mainWindowDisplayInfo = nil

---@type UnityEngine.Resolution[]
Screen.GetResolution = nil

---Should the cursor be visible?
---@type boolean
Screen.showCursor = nil

---Enable cursor locking
---@type boolean
Screen.lockCursor = nil

---Switches the screen resolution.
---@param width integer
---@param height integer
---@param fullscreenMode UnityEngine.FullScreenMode
---@param preferredRefreshRate UnityEngine.RefreshRate
---@return void
function Screen.SetResolution(width, height, fullscreenMode, preferredRefreshRate)
end

---Deprecated.
---@param width integer
---@param height integer
---@param fullscreenMode UnityEngine.FullScreenMode
---@param preferredRefreshRate integer
---@return void
function Screen.SetResolution(width, height, fullscreenMode, preferredRefreshRate)
end

---Switches the screen resolution.
---@param width integer
---@param height integer
---@param fullscreenMode UnityEngine.FullScreenMode
---@return void
function Screen.SetResolution(width, height, fullscreenMode)
end

---Deprecated.
---@param width integer
---@param height integer
---@param fullscreen boolean
---@param preferredRefreshRate integer
---@return void
function Screen.SetResolution(width, height, fullscreen, preferredRefreshRate)
end

---Switches the screen resolution.
---@param width integer
---@param height integer
---@param fullscreen boolean
---@return void
function Screen.SetResolution(width, height, fullscreen)
end

---Sets the given number of MSAA samples for the screen buffer.
---@param numSamples integer Number of anti-aliased samples requested for the screen buffer.
---@return void
function Screen.SetMSAASamples(numSamples)
end

---@param displayLayout System.Collections.Generic.List<UnityEngine.DisplayInfo>
---@return void
function Screen.GetDisplayLayout(displayLayout)
end

---@param display UnityEngine.DisplayInfo
---@param position UnityEngine.Vector2Int
---@return UnityEngine.AsyncOperation
function Screen.MoveMainWindowTo(display, position)
end


---Interface into the Legacy Input system.
---@class UnityEngine.Input: object
---@overload fun(): UnityEngine.Input
local Input = {}
---Enables/Disables mouse simulation with touches. By default this option is enabled.
---@type boolean
Input.simulateMouseWithTouches = nil

---Is any key or mouse button currently held down? (Read Only)
---@type boolean
Input.anyKey = nil

---Returns true the first frame the user hits any key or mouse button. (Read Only)
---@type boolean
Input.anyKeyDown = nil

---Returns the keyboard input entered this frame. (Read Only)
---@type string
Input.inputString = nil

---The current mouse position in pixel coordinates. (Read Only).
---@type UnityEngine.Vector3
Input.mousePosition = nil

---The current mouse position delta in pixel coordinates. (Read Only).
---@type UnityEngine.Vector3
Input.mousePositionDelta = nil

---The current mouse scroll delta. (Read Only)
---@type UnityEngine.Vector2
Input.mouseScrollDelta = nil

---Controls enabling and disabling of IME input composition.
---@type UnityEngine.IMECompositionMode
Input.imeCompositionMode = nil

---The current IME composition string being typed by the user.
---@type string
Input.compositionString = nil

---Does the user have an IME keyboard input source selected?
---@type boolean
Input.imeIsSelected = nil

---The current text input position used by IMEs to open windows.
---@type UnityEngine.Vector2
Input.compositionCursorPos = nil

---Property indicating whether keypresses are eaten by a textinput if it has focus (default true).
---@type boolean
Input.eatKeyPressOnTextFieldFocus = nil

---Indicates if a mouse device is detected.
---@type boolean
Input.mousePresent = nil

---Returns whether the device on which application is currently running supports touch input.
---@type boolean
Input.touchSupported = nil

---Returns the number of queued pen events that can be accessed by calling GetPenEvent().
---@type integer
Input.penEventCount = nil

---Number of touches. Guaranteed not to change throughout the frame. (Read Only)
---@type integer
Input.touchCount = nil

---Bool value which let's users check if touch pressure is supported.
---@type boolean
Input.touchPressureSupported = nil

---Returns true when Stylus Touch is supported by a device or platform.
---@type boolean
Input.stylusTouchSupported = nil

---Property indicating whether the system handles multiple touches.
---@type boolean
Input.multiTouchEnabled = nil

---@type boolean
Input.isGyroAvailable = nil

---Device physical orientation as reported by OS. (Read Only)
---@type UnityEngine.DeviceOrientation
Input.deviceOrientation = nil

---Last measured linear acceleration of a device in three-dimensional space. (Read Only)
---@type UnityEngine.Vector3
Input.acceleration = nil

---This property controls if input sensors should be compensated for screen orientation.
---@type boolean
Input.compensateSensors = nil

---Number of acceleration measurements which occurred during last frame.
---@type integer
Input.accelerationEventCount = nil

---Should  Back button quit the application?
---@type boolean
Input.backButtonLeavesApp = nil

---Property for accessing device location (handheld devices only). (Read Only)
---@type UnityEngine.LocationService
Input.location = nil

---Property for accessing compass (handheld devices only). (Read Only)
---@type UnityEngine.Compass
Input.compass = nil

---Returns default gyroscope.
---@type UnityEngine.Gyroscope
Input.gyro = nil

---Returns list of objects representing status of all touches during last frame. (Read Only) (Allocates temporary variables).
---@type UnityEngine.Touch[]
Input.touches = nil

---Returns list of acceleration measurements which occurred during the last frame. (Read Only) (Allocates temporary variables).
---@type UnityEngine.AccelerationEvent[]
Input.accelerationEvents = nil

---Returns the value of the virtual axis identified by axisName.
---@param axisName string
---@return number
function Input.GetAxis(axisName)
end

---Returns the value of the virtual axis identified by axisName with no smoothing filtering applied.
---@param axisName string
---@return number
function Input.GetAxisRaw(axisName)
end

---True when an axis has been pressed and not released.
---@param buttonName string The name of the button such as Jump.
---@return boolean
function Input.GetButton(buttonName)
end

---Returns true during the frame the user pressed down the virtual button identified by buttonName.
---@param buttonName string
---@return boolean
function Input.GetButtonDown(buttonName)
end

---Returns true the first frame the user releases the virtual button identified by buttonName.
---@param buttonName string
---@return boolean
function Input.GetButtonUp(buttonName)
end

---Returns whether the given mouse button is held down.
---@param button integer
---@return boolean
function Input.GetMouseButton(button)
end

---Returns true during the frame the user pressed the given mouse button.
---@param button integer
---@return boolean
function Input.GetMouseButtonDown(button)
end

---Returns true during the frame the user releases the given mouse button.
---@param button integer
---@return boolean
function Input.GetMouseButtonUp(button)
end

---Resets all input. After ResetInputAxes all axes return to 0 and all buttons return to 0 for one frame.
---@return void
function Input.ResetInputAxes()
end

---True if the joystick layout has been preconfigured; false otherwise.
---@param joystickName string The name of the joystick to check (returned by Input.GetJoystickNames).
---@return boolean
function Input.IsJoystickPreconfigured(joystickName)
end

---Returns an array of joystick and gamepad device names.
---@return string[]
function Input.GetJoystickNames()
end

---Touch details in the struct.
---@param index integer The touch input on the device screen.
---@return UnityEngine.Touch
function Input.GetTouch(index)
end

---Pen event details in the struct.
---@param index integer
---@return UnityEngine.PenData
function Input.GetPenEvent(index)
end

---Pen event details in the struct.
---@return UnityEngine.PenData
function Input.GetLastPenContactEvent()
end

---Clears the pen event queue.
---@return void
function Input.ResetPenEvents()
end

---Clears the last stored pen event.
---Calling this function may impact event handling for UIToolKit elements.
---@return void
function Input.ClearLastPenContactEvent()
end

---Returns specific acceleration measurement which occurred during last frame. (Does not allocate temporary variables).
---@param index integer
---@return UnityEngine.AccelerationEvent
function Input.GetAccelerationEvent(index)
end

---Returns true while the user holds down the key identified by the key KeyCode enum parameter.
---@param key UnityEngine.KeyCode
---@return boolean
function Input.GetKey(key)
end

---Returns true while the user holds down the key identified by name.
---@param name string
---@return boolean
function Input.GetKey(name)
end

---Returns true during the frame the user releases the key identified by the key KeyCode enum parameter.
---@param key UnityEngine.KeyCode
---@return boolean
function Input.GetKeyUp(key)
end

---Returns true during the frame the user releases the key identified by name.
---@param name string
---@return boolean
function Input.GetKeyUp(name)
end

---Returns true during the frame the user starts pressing down the key identified by the key KeyCode enum parameter.
---@param key UnityEngine.KeyCode
---@return boolean
function Input.GetKeyDown(key)
end

---Returns true during the frame the user starts pressing down the key identified by name.
---@param name string
---@return boolean
function Input.GetKeyDown(name)
end


---PlayerPrefs is a class that stores Player preferences between game sessions. It can store string, float and integer values into the user’s platform registry.
---@class UnityEngine.PlayerPrefs: object
---@overload fun(): UnityEngine.PlayerPrefs
local PlayerPrefs = {}
---Sets a single integer value for the preference identified by the given key. You can use PlayerPrefs.GetInt to retrieve this value.
---@param key string
---@param value integer
---@return void
function PlayerPrefs.SetInt(key, value)
end

---The int value that corresponds to the given key. Returns defaultValue if no int value is found for the given key.
---@param key string The key used to retrieve the corresponding int value in the player preferences.
---@param defaultValue integer The value to return when no int value is found for the given key in the player preferences.
---@return integer
function PlayerPrefs.GetInt(key, defaultValue)
end

---The int value that corresponds to the given key. Returns 0 if no int value is found for the given key.
---@param key string The key used to retrieve the corresponding int value in the player preferences.
---@return integer
function PlayerPrefs.GetInt(key)
end

---Sets the float value of the preference identified by the given key. You can use PlayerPrefs.GetFloat to retrieve this value.
---@param key string
---@param value number
---@return void
function PlayerPrefs.SetFloat(key, value)
end

---The float value corresponding to the given key. Returns defaultValue if no float value is found for the given key.
---@param key string The key used to retrieve the corresponding float value in the player preferences.
---@param defaultValue number The value to return when no float value is found for the given key in the player preferences.
---@return number
function PlayerPrefs.GetFloat(key, defaultValue)
end

---The float value corresponding to the given key. Returns 0,0f if no float value is found for the given key.
---@param key string The key used to retrieve the corresponding float value in the player preferences.
---@return number
function PlayerPrefs.GetFloat(key)
end

---Sets a single string value for the preference identified by the given key. You can use PlayerPrefs.GetString to retrieve this value.
---@param key string
---@param value string
---@return void
function PlayerPrefs.SetString(key, value)
end

---The string corresponding to the given key, defaultValue if no string is found for the given key.
---@param key string The key used to retrieve the corresponding string in the player preferences.
---@param defaultValue string The value to return when no string is found for the given key in the player preferences.
---@return string
function PlayerPrefs.GetString(key, defaultValue)
end

---The string corresponding to the given key, string.Empty if no string is found for the given key.
---@param key string The key used to retrieve the corresponding string in the player preferences.
---@return string
function PlayerPrefs.GetString(key)
end

---Returns true if the given key exists in PlayerPrefs, otherwise returns false.
---@param key string
---@return boolean
function PlayerPrefs.HasKey(key)
end

---Removes the given key from the PlayerPrefs. If the key does not exist, DeleteKey has no impact.
---@param key string
---@return void
function PlayerPrefs.DeleteKey(key)
end

---Removes all keys and values from the preferences. Use with caution.
---@return void
function PlayerPrefs.DeleteAll()
end

---Saves all modified preferences.
---@return void
function PlayerPrefs.Save()
end


---A collection of common math functions.
---@class UnityEngine.Mathf: System.ValueType
---@overload fun(): UnityEngine.Mathf
local Mathf = {}
---The well-known 3.14159265358979... value (Read Only).
---@type number
Mathf.PI = nil

---A representation of positive infinity (Read Only).
---@type number
Mathf.Infinity = nil

---A representation of negative infinity (Read Only).
---@type number
Mathf.NegativeInfinity = nil

---Degrees-to-radians conversion constant (Read Only).
---@type number
Mathf.Deg2Rad = nil

---Radians-to-degrees conversion constant (Read Only).
---@type number
Mathf.Rad2Deg = nil

---A tiny floating point value (Read Only).
---@type number
Mathf.Epsilon = nil

---Converts the given value from gamma (sRGB) to linear color space.
---@param value number
---@return number
function Mathf.GammaToLinearSpace(value)
end

---Converts the given value from linear to gamma (sRGB) color space.
---@param value number
---@return number
function Mathf.LinearToGammaSpace(value)
end

---Correlated Color Temperature as floating point RGB color.
---@param kelvin number Temperature in Kelvin. Range 1000 to 40000 Kelvin.
---@return UnityEngine.Color
function Mathf.CorrelatedColorTemperatureToRGB(kelvin)
end

---The converted half-precision float, stored in a 16-bit unsigned integer.
---@param val number The floating point value to convert.
---@return ushort
function Mathf.FloatToHalf(val)
end

---The decoded 32-bit float.
---@param val ushort The half precision value to convert.
---@return number
function Mathf.HalfToFloat(val)
end

---Value between 0.0 and 1.0. (Return value might be slightly below 0.0 or beyond 1.0.)
---@param x number X-coordinate of sample point.
---@param y number Y-coordinate of sample point.
---@return number
function Mathf.PerlinNoise(x, y)
end

---A value in the range of 0.0 and 1.0. The value might be slightly higher or lower than this range.
---@param x number The X-coordinate of the given sample point.
---@return number
function Mathf.PerlinNoise1D(x)
end

---The return value between -1 and +1.
---@param f number The input angle, in radians.
---@return number
function Mathf.Sin(f)
end

---The return value between -1 and 1.
---@param f number The input angle, in radians.
---@return number
function Mathf.Cos(f)
end

---Returns the tangent of angle f in radians.
---@param f number
---@return number
function Mathf.Tan(f)
end

---Returns the arc-sine of f - the angle in radians whose sine is f.
---@param f number
---@return number
function Mathf.Asin(f)
end

---Returns the arc-cosine of f - the angle in radians whose cosine is f.
---@param f number
---@return number
function Mathf.Acos(f)
end

---Returns the arc-tangent of f - the angle in radians whose tangent is f.
---@param f number
---@return number
function Mathf.Atan(f)
end

---Returns the angle in radians whose Tan is y/x.
---@param y number
---@param x number
---@return number
function Mathf.Atan2(y, x)
end

---Returns square root of f.
---@param f number
---@return number
function Mathf.Sqrt(f)
end

---Returns the absolute value of f.
---@param f number
---@return number
function Mathf.Abs(f)
end

---Returns the absolute value of value.
---@param value integer
---@return integer
function Mathf.Abs(value)
end

---Returns the smallest of two or more values.
---@param a number
---@param b number
---@return number
function Mathf.Min(a, b)
end

---Returns the smallest of two or more values.
---@param values float[]
---@return number
function Mathf.Min(values)
end

---Returns the smallest of two or more values.
---@param a integer
---@param b integer
---@return integer
function Mathf.Min(a, b)
end

---Returns the smallest of two or more values.
---@param values int[]
---@return integer
function Mathf.Min(values)
end

---Returns the largest of two or more values. When comparing negative values, values closer to zero are considered larger.
---@param a number
---@param b number
---@return number
function Mathf.Max(a, b)
end

---Returns the largest of two or more values. When comparing negative values, values closer to zero are considered larger.
---@param values float[]
---@return number
function Mathf.Max(values)
end

---Returns the largest value. When comparing negative values, values closer to zero are considered larger.
---@param a integer
---@param b integer
---@return integer
function Mathf.Max(a, b)
end

---Returns the largest value. When comparing negative values, values closer to zero are considered larger.
---@param values int[]
---@return integer
function Mathf.Max(values)
end

---Returns f raised to power p.
---@param f number
---@param p number
---@return number
function Mathf.Pow(f, p)
end

---Returns e raised to the specified power.
---@param power number
---@return number
function Mathf.Exp(power)
end

---Returns the logarithm of a specified number in a specified base.
---@param f number
---@param p number
---@return number
function Mathf.Log(f, p)
end

---Returns the natural (base e) logarithm of a specified number.
---@param f number
---@return number
function Mathf.Log(f)
end

---Returns the base 10 logarithm of a specified number.
---@param f number
---@return number
function Mathf.Log10(f)
end

---Returns the smallest integer greater than or equal to f.
---@param f number
---@return number
function Mathf.Ceil(f)
end

---Returns the largest integer smaller than or equal to f.
---@param f number
---@return number
function Mathf.Floor(f)
end

---Returns f rounded to the nearest integer.
---@param f number
---@return number
function Mathf.Round(f)
end

---Returns the smallest integer greater to or equal to f.
---@param f number
---@return integer
function Mathf.CeilToInt(f)
end

---Returns the largest integer smaller to or equal to f.
---@param f number
---@return integer
function Mathf.FloorToInt(f)
end

---Returns f rounded to the nearest integer.
---@param f number
---@return integer
function Mathf.RoundToInt(f)
end

---Returns the sign of f.
---@param f number
---@return number
function Mathf.Sign(f)
end

---The float result between the minimum and maximum values.
---@param value number The floating point value to restrict inside the range defined by the minimum and maximum values.
---@param min number The minimum floating point value to compare against.
---@param max number The maximum floating point value to compare against.
---@return number
function Mathf.Clamp(value, min, max)
end

---The int result between min and max values.
---@param value integer The integer point value to restrict inside the min-to-max range.
---@param min integer The minimum integer point value to compare against.
---@param max integer The maximum  integer point value to compare against.
---@return integer
function Mathf.Clamp(value, min, max)
end

---Clamps value between 0 and 1 and returns value.
---@param value number
---@return number
function Mathf.Clamp01(value)
end

---The interpolated float result between the two float values.
---@param a number The start value.
---@param b number The end value.
---@param t number The interpolation value between the two floats.
---@return number
function Mathf.Lerp(a, b, t)
end

---The float value as a result from the linear interpolation.
---@param a number The start value.
---@param b number The end value.
---@param t number The interpolation between the two floats.
---@return number
function Mathf.LerpUnclamped(a, b, t)
end

---Returns the interpolated float result between angle a and angle b, based on the interpolation value t.
---@param a number The start angle. A float expressed in degrees.
---@param b number The end angle. A float expressed in degrees.
---@param t number The interpolation value between the start and end angles. This value is clamped to the range [0, 1].
---@return number
function Mathf.LerpAngle(a, b, t)
end

---Moves a value current towards target.
---@param current number The current value.
---@param target number The value to move towards.
---@param maxDelta number The maximum change applied to the current value.
---@return number
function Mathf.MoveTowards(current, target, maxDelta)
end

---Same as MoveTowards but makes sure the values interpolate correctly when they wrap around 360 degrees.
---@param current number
---@param target number
---@param maxDelta number
---@return number
function Mathf.MoveTowardsAngle(current, target, maxDelta)
end

---The interpolated float result between from and to.
---@param from number The start of the range.
---@param to number The end of the range.
---@param t number The interpolation value between the from and to range limits.
---@return number
function Mathf.SmoothStep(from, to, t)
end

---@param value number
---@param absmax number
---@param gamma number
---@return number
function Mathf.Gamma(value, absmax, gamma)
end

---Compares two floating point values and returns true if they are similar.
---@param a number
---@param b number
---@return boolean
function Mathf.Approximately(a, b)
end

---@param current number
---@param target number
---@param currentVelocity number
---@param smoothTime number
---@param maxSpeed number
---@return numberfloat
function Mathf.SmoothDamp(current, target, currentVelocity, smoothTime, maxSpeed)
end

---@param current number
---@param target number
---@param currentVelocity number
---@param smoothTime number
---@return numberfloat
function Mathf.SmoothDamp(current, target, currentVelocity, smoothTime)
end

---@param current number
---@param target number
---@param currentVelocity number
---@param smoothTime number
---@param maxSpeed number
---@param deltaTime number
---@return numberfloat
function Mathf.SmoothDamp(current, target, currentVelocity, smoothTime, maxSpeed, deltaTime)
end

---@param current number
---@param target number
---@param currentVelocity number
---@param smoothTime number
---@param maxSpeed number
---@return numberfloat
function Mathf.SmoothDampAngle(current, target, currentVelocity, smoothTime, maxSpeed)
end

---@param current number
---@param target number
---@param currentVelocity number
---@param smoothTime number
---@return numberfloat
function Mathf.SmoothDampAngle(current, target, currentVelocity, smoothTime)
end

---@param current number
---@param target number
---@param currentVelocity number
---@param smoothTime number
---@param maxSpeed number
---@param deltaTime number
---@return numberfloat
function Mathf.SmoothDampAngle(current, target, currentVelocity, smoothTime, maxSpeed, deltaTime)
end

---Loops the value t, so that it is never larger than length and never smaller than 0.
---@param t number
---@param length number
---@return number
function Mathf.Repeat(t, length)
end

---PingPong returns a value that increments and decrements between zero and the length. It follows the triangle wave formula where the bottom is set to zero and the peak is set to length.
---@param t number
---@param length number
---@return number
function Mathf.PingPong(t, length)
end

---A value between zero and one, representing where the "value" parameter falls within the range defined by a and b.
---@param a number The start of the range.
---@param b number The end of the range.
---@param value number The point within the range you want to calculate.
---@return number
function Mathf.InverseLerp(a, b, value)
end

---A value between -179 and 180, in degrees.
---@param current number The current angle in degrees.
---@param target number The target angle in degrees.
---@return number
function Mathf.DeltaAngle(current, target)
end

---Returns the next power of two that is equal to, or greater than, the argument.
---@param value integer
---@return integer
function Mathf.NextPowerOfTwo(value)
end

---Returns the closest power of two value.
---@param value integer
---@return integer
function Mathf.ClosestPowerOfTwo(value)
end

---Returns true if the value is power of two.
---@param value integer
---@return boolean
function Mathf.IsPowerOfTwo(value)
end


---Easily generate random data for games.
---@class UnityEngine.Random: object
---@overload fun(): UnityEngine.Random
local Random = {}
---Gets or sets the full internal state of the random number generator.
---@type UnityEngine.Random.State
Random.state = nil

---Returns a random float within [0.0..1.0] (range is inclusive) (Read Only).
---@type number
Random.value = nil

---Returns a random point inside or on a sphere with radius 1.0 (Read Only).
---@type UnityEngine.Vector3
Random.insideUnitSphere = nil

---Returns a random point inside or on a circle with radius 1.0 (Read Only).
---@type UnityEngine.Vector2
Random.insideUnitCircle = nil

---Returns a random point on the surface of a sphere with radius 1.0 (Read Only).
---@type UnityEngine.Vector3
Random.onUnitSphere = nil

---Returns a random rotation (Read Only).
---@type UnityEngine.Quaternion
Random.rotation = nil

---Returns a random rotation with uniform distribution (Read Only).
---@type UnityEngine.Quaternion
Random.rotationUniform = nil

---@type integer
Random.seed = nil

---Initializes the random number generator state with a seed.
---@param seed integer Seed used to initialize the random number generator.
---@return void
function Random.InitState(seed)
end

---Returns a random float within [minInclusive..maxInclusive] (range is inclusive).
---@param minInclusive number
---@param maxInclusive number
---@return number
function Random.Range(minInclusive, maxInclusive)
end

---Return a random int within [minInclusive..maxExclusive) (Read Only).
---@param minInclusive integer
---@param maxExclusive integer
---@return integer
function Random.Range(minInclusive, maxExclusive)
end

---@param min number
---@param max number
---@return number
function Random.RandomRange(min, max)
end

---@param min integer
---@param max integer
---@return integer
function Random.RandomRange(min, max)
end

---A random color with HSV and alpha values in the (inclusive) input ranges. Values for each component are derived via linear interpolation of value.
---@return UnityEngine.Color
function Random.ColorHSV()
end

---A random color with HSV and alpha values in the (inclusive) input ranges. Values for each component are derived via linear interpolation of value.
---@param hueMin number Minimum hue [0..1].
---@param hueMax number Maximum hue [0..1].
---@return UnityEngine.Color
function Random.ColorHSV(hueMin, hueMax)
end

---A random color with HSV and alpha values in the (inclusive) input ranges. Values for each component are derived via linear interpolation of value.
---@param hueMin number Minimum hue [0..1].
---@param hueMax number Maximum hue [0..1].
---@param saturationMin number Minimum saturation [0..1].
---@param saturationMax number Maximum saturation [0..1].
---@return UnityEngine.Color
function Random.ColorHSV(hueMin, hueMax, saturationMin, saturationMax)
end

---A random color with HSV and alpha values in the (inclusive) input ranges. Values for each component are derived via linear interpolation of value.
---@param hueMin number Minimum hue [0..1].
---@param hueMax number Maximum hue [0..1].
---@param saturationMin number Minimum saturation [0..1].
---@param saturationMax number Maximum saturation [0..1].
---@param valueMin number Minimum value [0..1].
---@param valueMax number Maximum value [0..1].
---@return UnityEngine.Color
function Random.ColorHSV(hueMin, hueMax, saturationMin, saturationMax, valueMin, valueMax)
end

---A random color with HSV and alpha values in the (inclusive) input ranges. Values for each component are derived via linear interpolation of value.
---@param hueMin number Minimum hue [0..1].
---@param hueMax number Maximum hue [0..1].
---@param saturationMin number Minimum saturation [0..1].
---@param saturationMax number Maximum saturation [0..1].
---@param valueMin number Minimum value [0..1].
---@param valueMax number Maximum value [0..1].
---@param alphaMin number Minimum alpha [0..1].
---@param alphaMax number Maximum alpha [0..1].
---@return UnityEngine.Color
function Random.ColorHSV(hueMin, hueMax, saturationMin, saturationMax, valueMin, valueMax, alphaMin, alphaMax)
end


---MonoBehaviour.StartCoroutine returns a Coroutine. Instances of this class are only used to reference these coroutines, and do not hold any exposed properties or functions.
---@class UnityEngine.Coroutine: UnityEngine.YieldInstruction
---@overload fun(): UnityEngine.Coroutine
local Coroutine = {}

---Suspends the coroutine execution for the specified scaled time in seconds.
---@class UnityEngine.WaitForSeconds: UnityEngine.YieldInstruction
---@overload fun(seconds: number): UnityEngine.WaitForSeconds
local WaitForSeconds = {}

---Suspends a coroutine until the end of the frame after Unity has rendered every Camera and GUI, just before displaying the frame on screen.
---@class UnityEngine.WaitForEndOfFrame: UnityEngine.YieldInstruction
---@overload fun(): UnityEngine.WaitForEndOfFrame
local WaitForEndOfFrame = {}

---Suspends coroutine execution until the next fixed update.
---@class UnityEngine.WaitForFixedUpdate: UnityEngine.YieldInstruction
---@overload fun(): UnityEngine.WaitForFixedUpdate
local WaitForFixedUpdate = {}

---Element that can be used for screen rendering.
---@class UnityEngine.Canvas: UnityEngine.Behaviour
---@overload fun(): UnityEngine.Canvas
local Canvas = {}
---Is the Canvas in World or Overlay mode?
---@type UnityEngine.RenderMode
Canvas.renderMode = nil

---Is this the root Canvas?
---@type boolean
Canvas.isRootCanvas = nil

---Get the render rect for the Canvas.
---@type UnityEngine.Rect
Canvas.pixelRect = nil

---Scales the entire canvas, ensuring it fits the screen. It only applies when Canvas.renderMode is set to Screen Space.
---@type number
Canvas.scaleFactor = nil

---The number of pixels per unit that is considered the default.
---@type number
Canvas.referencePixelsPerUnit = nil

---Allows for nested canvases to override pixelPerfect settings inherited from parent canvases.
---@type boolean
Canvas.overridePixelPerfect = nil

---Should the Canvas vertex color always be in gamma space before passing to the UI shaders in linear color space work flow.
---@type boolean
Canvas.vertexColorAlwaysGammaSpace = nil

---Forces pixel alignment for elements in the canvas. It only applies when Canvas.renderMode is set to Screen Space.
---@type boolean
Canvas.pixelPerfect = nil

---How far away from the camera is the Canvas generated? It only applies when Canvas.renderMode is set to RenderMode.ScreenSpaceCamera.
---@type number
Canvas.planeDistance = nil

---The render order in which the canvas is being emitted to the Scene. (Read Only)
---@type integer
Canvas.renderOrder = nil

---Allows for nested canvases to override the Canvas.sortingOrder from parent canvases.
---@type boolean
Canvas.overrideSorting = nil

---Canvas' order within a sorting layer.
---@type integer
Canvas.sortingOrder = nil

---For Overlay mode, display index on which the UI canvas will appear.
---@type integer
Canvas.targetDisplay = nil

---Unique ID of the Canvas' sorting layer.
---@type integer
Canvas.sortingLayerID = nil

---Cached calculated value based upon SortingLayerID.
---@type integer
Canvas.cachedSortingLayerValue = nil

---Get or set the mask of additional shader channels to be used when creating the Canvas mesh.
---@type UnityEngine.AdditionalCanvasShaderChannels
Canvas.additionalShaderChannels = nil

---Name of the Canvas' sorting layer.
---@type string
Canvas.sortingLayerName = nil

---Returns the Canvas closest to root, by checking through each parent and returning the last canvas found. If no other canvas is found then the canvas will return itself.
---@type UnityEngine.Canvas
Canvas.rootCanvas = nil

---Provides the pixel dimensions of the display area where the UI canvas is rendered.
---@type UnityEngine.Vector2
Canvas.renderingDisplaySize = nil

---Should the Canvas size be updated based on the render target when a manual Camera.Render call is performed.
---@type UnityEngine.StandaloneRenderResize
Canvas.updateRectTransformForStandalone = nil

---Camera used for sizing the Canvas when in Screen Space - Camera. Also used as the Camera that events will be sent through for a World Space Canvas.
---@type UnityEngine.Camera
Canvas.worldCamera = nil

---The normalized grid size that the canvas will split the renderable area into.
---@type number
Canvas.normalizedSortingGridSize = nil

---The normalized grid size that the canvas will split the renderable area into.
---@type integer
Canvas.sortingGridNormalizedSize = nil

---@param value UnityEngine.Canvas.WillRenderCanvases
---@return void
function Canvas.add_preWillRenderCanvases(value)
end

---@param value UnityEngine.Canvas.WillRenderCanvases
---@return void
function Canvas.remove_preWillRenderCanvases(value)
end

---@param value UnityEngine.Canvas.WillRenderCanvases
---@return void
function Canvas.add_willRenderCanvases(value)
end

---@param value UnityEngine.Canvas.WillRenderCanvases
---@return void
function Canvas.remove_willRenderCanvases(value)
end

---Returns the default material that can be used for rendering text elements on the Canvas.
---@return UnityEngine.Material
function Canvas.GetDefaultCanvasTextMaterial()
end

---Returns the default material that can be used for rendering normal elements on the Canvas.
---@return UnityEngine.Material
function Canvas.GetDefaultCanvasMaterial()
end

---The generated ETC1 Material from the Canvas.
---@return UnityEngine.Material
function Canvas.GetETC1SupportedCanvasMaterial()
end

---Force all canvases to update their content.
---@return void
function Canvas.ForceUpdateCanvases()
end


---A Canvas placable element that can be used to modify children Alpha, Raycasting, Enabled state.
---@class UnityEngine.CanvasGroup: UnityEngine.Behaviour, UnityEngine.ICanvasRaycastFilter
---@overload fun(): UnityEngine.CanvasGroup
local CanvasGroup = {}
---Set the alpha of the group.
---@type number
CanvasGroup.alpha = nil

---Is the group interactable (are the elements beneath the group enabled).
---@type boolean
CanvasGroup.interactable = nil

---Does this group block raycasting (allow collision).
---@type boolean
CanvasGroup.blocksRaycasts = nil

---Should the group ignore parent groups?
---@type boolean
CanvasGroup.ignoreParentGroups = nil

---Returns true if the Group allows raycasts.
---@param sp UnityEngine.Vector2
---@param eventCamera UnityEngine.Camera
---@return boolean
function CanvasGroup:IsRaycastLocationValid(sp, eventCamera)
end


---Position, size, anchor and pivot information for a rectangle.
---@class UnityEngine.RectTransform: UnityEngine.Transform, System.Collections.IEnumerable
---@overload fun(): UnityEngine.RectTransform
local RectTransform = {}
---The calculated rectangle in the local space of the Transform.
---@type UnityEngine.Rect
RectTransform.rect = nil

---The normalized position in the parent RectTransform that the lower left corner is anchored to.
---@type UnityEngine.Vector2
RectTransform.anchorMin = nil

---The normalized position in the parent RectTransform that the upper right corner is anchored to.
---@type UnityEngine.Vector2
RectTransform.anchorMax = nil

---The position of the pivot of this RectTransform relative to the anchor reference point.
---@type UnityEngine.Vector2
RectTransform.anchoredPosition = nil

---The size of this RectTransform relative to the distances between the anchors.
---@type UnityEngine.Vector2
RectTransform.sizeDelta = nil

---The normalized position in this RectTransform that it rotates around.
---@type UnityEngine.Vector2
RectTransform.pivot = nil

---The 3D position of the pivot of this RectTransform relative to the anchor reference point.
---@type UnityEngine.Vector3
RectTransform.anchoredPosition3D = nil

---The offset of the lower left corner of the rectangle relative to the lower left anchor.
---@type UnityEngine.Vector2
RectTransform.offsetMin = nil

---The offset of the upper right corner of the rectangle relative to the upper right anchor.
---@type UnityEngine.Vector2
RectTransform.offsetMax = nil

---The object that is driving the values of this RectTransform. Value is null if not driven.
---@type UnityEngine.Object
RectTransform.drivenByObject = nil

---@param value UnityEngine.RectTransform.ReapplyDrivenProperties
---@return void
function RectTransform.add_reapplyDrivenProperties(value)
end

---@param value UnityEngine.RectTransform.ReapplyDrivenProperties
---@return void
function RectTransform.remove_reapplyDrivenProperties(value)
end

---Force the recalculation of RectTransforms internal data.
---@return void
function RectTransform:ForceUpdateRectTransforms()
end

---Get the corners of the calculated rectangle in the local space of its Transform.
---@param fourCornersArray UnityEngine.Vector3[] The array that corners are filled into.
---@return void
function RectTransform:GetLocalCorners(fourCornersArray)
end

---Get the corners of the calculated rectangle in world space.
---@param fourCornersArray UnityEngine.Vector3[] The array that corners are filled into.
---@return void
function RectTransform:GetWorldCorners(fourCornersArray)
end

---@param edge UnityEngine.RectTransform.Edge
---@param inset number
---@param size number
---@return void
function RectTransform:SetInsetAndSizeFromParentEdge(edge, inset, size)
end

---@param axis UnityEngine.RectTransform.Axis
---@param size number
---@return void
function RectTransform:SetSizeWithCurrentAnchors(axis, size)
end


---@class UnityEngine.UI.Button: UnityEngine.UI.Selectable, UnityEngine.EventSystems.IMoveHandler, UnityEngine.EventSystems.IPointerDownHandler, UnityEngine.EventSystems.IPointerUpHandler, UnityEngine.EventSystems.IPointerEnterHandler, UnityEngine.EventSystems.IPointerExitHandler, UnityEngine.EventSystems.ISelectHandler, UnityEngine.EventSystems.IDeselectHandler, UnityEngine.EventSystems.IPointerClickHandler, UnityEngine.EventSystems.ISubmitHandler, UnityEngine.EventSystems.IEventSystemHandler
---@overload fun(): UnityEngine.UI.Button
local Button = {}
---@type UnityEngine.UI.Button.ButtonClickedEvent
Button.onClick = nil

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function Button:OnPointerClick(eventData)
end

---@param eventData UnityEngine.EventSystems.BaseEventData
---@return void
function Button:OnSubmit(eventData)
end


---@class UnityEngine.UI.Text: UnityEngine.UI.MaskableGraphic, UnityEngine.UI.ICanvasElement, UnityEngine.UI.IClippable, UnityEngine.UI.IMaskable, UnityEngine.UI.IMaterialModifier, UnityEngine.UI.ILayoutElement
---@overload fun(): UnityEngine.UI.Text
local Text = {}
---@type UnityEngine.TextGenerator
Text.cachedTextGenerator = nil

---@type UnityEngine.TextGenerator
Text.cachedTextGeneratorForLayout = nil

---@type UnityEngine.Texture
Text.mainTexture = nil

---@type UnityEngine.Font
Text.font = nil

---@type string
Text.text = nil

---@type boolean
Text.supportRichText = nil

---@type boolean
Text.resizeTextForBestFit = nil

---@type integer
Text.resizeTextMinSize = nil

---@type integer
Text.resizeTextMaxSize = nil

---@type UnityEngine.TextAnchor
Text.alignment = nil

---@type boolean
Text.alignByGeometry = nil

---@type integer
Text.fontSize = nil

---@type UnityEngine.HorizontalWrapMode
Text.horizontalOverflow = nil

---@type UnityEngine.VerticalWrapMode
Text.verticalOverflow = nil

---@type number
Text.lineSpacing = nil

---@type UnityEngine.FontStyle
Text.fontStyle = nil

---@type number
Text.pixelsPerUnit = nil

---@type number
Text.minWidth = nil

---@type number
Text.preferredWidth = nil

---@type number
Text.flexibleWidth = nil

---@type number
Text.minHeight = nil

---@type number
Text.preferredHeight = nil

---@type number
Text.flexibleHeight = nil

---@type integer
Text.layoutPriority = nil

---@return void
function Text:FontTextureChanged()
end

---@param extents UnityEngine.Vector2
---@return UnityEngine.TextGenerationSettings
function Text:GetGenerationSettings(extents)
end

---@param anchor UnityEngine.TextAnchor
---@return UnityEngine.Vector2
function Text.GetTextAnchorPivot(anchor)
end

---@return void
function Text:CalculateLayoutInputHorizontal()
end

---@return void
function Text:CalculateLayoutInputVertical()
end

---@return void
function Text:OnRebuildRequested()
end


---@class UnityEngine.UI.Image: UnityEngine.UI.MaskableGraphic, UnityEngine.UI.ICanvasElement, UnityEngine.UI.IClippable, UnityEngine.UI.IMaskable, UnityEngine.UI.IMaterialModifier, UnityEngine.ISerializationCallbackReceiver, UnityEngine.UI.ILayoutElement, UnityEngine.ICanvasRaycastFilter
---@overload fun(): UnityEngine.UI.Image
local Image = {}
---@type UnityEngine.Sprite
Image.sprite = nil

---@type UnityEngine.Sprite
Image.overrideSprite = nil

---@type UnityEngine.UI.Image.Type
Image.type = nil

---@type boolean
Image.preserveAspect = nil

---@type boolean
Image.fillCenter = nil

---@type UnityEngine.UI.Image.FillMethod
Image.fillMethod = nil

---@type number
Image.fillAmount = nil

---@type boolean
Image.fillClockwise = nil

---@type integer
Image.fillOrigin = nil

---@type number
Image.eventAlphaThreshold = nil

---@type number
Image.alphaHitTestMinimumThreshold = nil

---@type boolean
Image.useSpriteMesh = nil

---@type UnityEngine.Material
Image.defaultETC1GraphicMaterial = nil

---@type UnityEngine.Texture
Image.mainTexture = nil

---@type boolean
Image.hasBorder = nil

---@type number
Image.pixelsPerUnitMultiplier = nil

---@type number
Image.pixelsPerUnit = nil

---@type UnityEngine.Material
Image.material = nil

---@type number
Image.minWidth = nil

---@type number
Image.preferredWidth = nil

---@type number
Image.flexibleWidth = nil

---@type number
Image.minHeight = nil

---@type number
Image.preferredHeight = nil

---@type number
Image.flexibleHeight = nil

---@type integer
Image.layoutPriority = nil

---@return void
function Image:DisableSpriteOptimizations()
end

---@return void
function Image:OnBeforeSerialize()
end

---@return void
function Image:OnAfterDeserialize()
end

---@return void
function Image:SetNativeSize()
end

---@return void
function Image:CalculateLayoutInputHorizontal()
end

---@return void
function Image:CalculateLayoutInputVertical()
end

---@param screenPoint UnityEngine.Vector2
---@param eventCamera UnityEngine.Camera
---@return boolean
function Image:IsRaycastLocationValid(screenPoint, eventCamera)
end


---@class UnityEngine.UI.Slider: UnityEngine.UI.Selectable, UnityEngine.EventSystems.IMoveHandler, UnityEngine.EventSystems.IPointerDownHandler, UnityEngine.EventSystems.IPointerUpHandler, UnityEngine.EventSystems.IPointerEnterHandler, UnityEngine.EventSystems.IPointerExitHandler, UnityEngine.EventSystems.ISelectHandler, UnityEngine.EventSystems.IDeselectHandler, UnityEngine.EventSystems.IDragHandler, UnityEngine.EventSystems.IInitializePotentialDragHandler, UnityEngine.EventSystems.IEventSystemHandler, UnityEngine.UI.ICanvasElement
---@overload fun(): UnityEngine.UI.Slider
local Slider = {}
---@type UnityEngine.RectTransform
Slider.fillRect = nil

---@type UnityEngine.RectTransform
Slider.handleRect = nil

---@type UnityEngine.UI.Slider.Direction
Slider.direction = nil

---@type number
Slider.minValue = nil

---@type number
Slider.maxValue = nil

---@type boolean
Slider.wholeNumbers = nil

---@type number
Slider.value = nil

---@type number
Slider.normalizedValue = nil

---@type UnityEngine.UI.Slider.SliderEvent
Slider.onValueChanged = nil

---@param input number
---@return void
function Slider:SetValueWithoutNotify(input)
end

---@param executing UnityEngine.UI.CanvasUpdate
---@return void
function Slider:Rebuild(executing)
end

---@return void
function Slider:LayoutComplete()
end

---@return void
function Slider:GraphicUpdateComplete()
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function Slider:OnPointerDown(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function Slider:OnDrag(eventData)
end

---@param eventData UnityEngine.EventSystems.AxisEventData
---@return void
function Slider:OnMove(eventData)
end

---@return UnityEngine.UI.Selectable
function Slider:FindSelectableOnLeft()
end

---@return UnityEngine.UI.Selectable
function Slider:FindSelectableOnRight()
end

---@return UnityEngine.UI.Selectable
function Slider:FindSelectableOnUp()
end

---@return UnityEngine.UI.Selectable
function Slider:FindSelectableOnDown()
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function Slider:OnInitializePotentialDrag(eventData)
end

---@param direction UnityEngine.UI.Slider.Direction
---@param includeRectLayouts boolean
---@return void
function Slider:SetDirection(direction, includeRectLayouts)
end


---@class UnityEngine.UI.Toggle: UnityEngine.UI.Selectable, UnityEngine.EventSystems.IMoveHandler, UnityEngine.EventSystems.IPointerDownHandler, UnityEngine.EventSystems.IPointerUpHandler, UnityEngine.EventSystems.IPointerEnterHandler, UnityEngine.EventSystems.IPointerExitHandler, UnityEngine.EventSystems.ISelectHandler, UnityEngine.EventSystems.IDeselectHandler, UnityEngine.EventSystems.IPointerClickHandler, UnityEngine.EventSystems.ISubmitHandler, UnityEngine.EventSystems.IEventSystemHandler, UnityEngine.UI.ICanvasElement
---@overload fun(): UnityEngine.UI.Toggle
local Toggle = {}
---@type UnityEngine.UI.Toggle.ToggleTransition
Toggle.toggleTransition = nil

---@type UnityEngine.UI.Graphic
Toggle.graphic = nil

---@type UnityEngine.UI.Toggle.ToggleEvent
Toggle.onValueChanged = nil

---@type UnityEngine.UI.ToggleGroup
Toggle.group = nil

---@type boolean
Toggle.isOn = nil

---@param executing UnityEngine.UI.CanvasUpdate
---@return void
function Toggle:Rebuild(executing)
end

---@return void
function Toggle:LayoutComplete()
end

---@return void
function Toggle:GraphicUpdateComplete()
end

---@param value boolean
---@return void
function Toggle:SetIsOnWithoutNotify(value)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function Toggle:OnPointerClick(eventData)
end

---@param eventData UnityEngine.EventSystems.BaseEventData
---@return void
function Toggle:OnSubmit(eventData)
end


---@class UnityEngine.UI.InputField: UnityEngine.UI.Selectable, UnityEngine.EventSystems.IMoveHandler, UnityEngine.EventSystems.IPointerDownHandler, UnityEngine.EventSystems.IPointerUpHandler, UnityEngine.EventSystems.IPointerEnterHandler, UnityEngine.EventSystems.IPointerExitHandler, UnityEngine.EventSystems.ISelectHandler, UnityEngine.EventSystems.IDeselectHandler, UnityEngine.EventSystems.IUpdateSelectedHandler, UnityEngine.EventSystems.IBeginDragHandler, UnityEngine.EventSystems.IDragHandler, UnityEngine.EventSystems.IEndDragHandler, UnityEngine.EventSystems.IPointerClickHandler, UnityEngine.EventSystems.ISubmitHandler, UnityEngine.EventSystems.IEventSystemHandler, UnityEngine.UI.ICanvasElement, UnityEngine.UI.ILayoutElement
---@overload fun(): UnityEngine.UI.InputField
local InputField = {}
---@type boolean
InputField.shouldHideMobileInput = nil

---@type boolean
InputField.shouldActivateOnSelect = nil

---@type string
InputField.text = nil

---@type boolean
InputField.isFocused = nil

---@type number
InputField.caretBlinkRate = nil

---@type integer
InputField.caretWidth = nil

---@type UnityEngine.UI.Text
InputField.textComponent = nil

---@type UnityEngine.UI.Graphic
InputField.placeholder = nil

---@type UnityEngine.Color
InputField.caretColor = nil

---@type boolean
InputField.customCaretColor = nil

---@type UnityEngine.Color
InputField.selectionColor = nil

---@type UnityEngine.UI.InputField.EndEditEvent
InputField.onEndEdit = nil

---@type UnityEngine.UI.InputField.SubmitEvent
InputField.onSubmit = nil

---@type UnityEngine.UI.InputField.OnChangeEvent
InputField.onValueChange = nil

---@type UnityEngine.UI.InputField.OnChangeEvent
InputField.onValueChanged = nil

---@type UnityEngine.UI.InputField.OnValidateInput
InputField.onValidateInput = nil

---@type integer
InputField.characterLimit = nil

---@type UnityEngine.UI.InputField.ContentType
InputField.contentType = nil

---@type UnityEngine.UI.InputField.LineType
InputField.lineType = nil

---@type UnityEngine.UI.InputField.InputType
InputField.inputType = nil

---@type UnityEngine.TouchScreenKeyboard
InputField.touchScreenKeyboard = nil

---@type UnityEngine.TouchScreenKeyboardType
InputField.keyboardType = nil

---@type UnityEngine.UI.InputField.CharacterValidation
InputField.characterValidation = nil

---@type boolean
InputField.readOnly = nil

---@type boolean
InputField.multiLine = nil

---@type char
InputField.asteriskChar = nil

---@type boolean
InputField.wasCanceled = nil

---@type integer
InputField.caretSelectPosition = nil

---@type integer
InputField.caretPosition = nil

---@type integer
InputField.selectionAnchorPosition = nil

---@type integer
InputField.selectionFocusPosition = nil

---@type number
InputField.minWidth = nil

---@type number
InputField.preferredWidth = nil

---@type number
InputField.flexibleWidth = nil

---@type number
InputField.minHeight = nil

---@type number
InputField.preferredHeight = nil

---@type number
InputField.flexibleHeight = nil

---@type integer
InputField.layoutPriority = nil

---@param input string
---@return void
function InputField:SetTextWithoutNotify(input)
end

---@param shift boolean
---@return void
function InputField:MoveTextEnd(shift)
end

---@param shift boolean
---@return void
function InputField:MoveTextStart(shift)
end

---@param screen UnityEngine.Vector2
---@return UnityEngine.Vector2
function InputField:ScreenToLocal(screen)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function InputField:OnBeginDrag(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function InputField:OnDrag(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function InputField:OnEndDrag(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function InputField:OnPointerDown(eventData)
end

---@param e UnityEngine.Event
---@return void
function InputField:ProcessEvent(e)
end

---@param eventData UnityEngine.EventSystems.BaseEventData
---@return void
function InputField:OnUpdateSelected(eventData)
end

---@return void
function InputField:ForceLabelUpdate()
end

---@param update UnityEngine.UI.CanvasUpdate
---@return void
function InputField:Rebuild(update)
end

---@return void
function InputField:LayoutComplete()
end

---@return void
function InputField:GraphicUpdateComplete()
end

---@return void
function InputField:ActivateInputField()
end

---@param eventData UnityEngine.EventSystems.BaseEventData
---@return void
function InputField:OnSelect(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function InputField:OnPointerClick(eventData)
end

---@return void
function InputField:DeactivateInputField()
end

---@param eventData UnityEngine.EventSystems.BaseEventData
---@return void
function InputField:OnDeselect(eventData)
end

---@param eventData UnityEngine.EventSystems.BaseEventData
---@return void
function InputField:OnSubmit(eventData)
end

---@return void
function InputField:CalculateLayoutInputHorizontal()
end

---@return void
function InputField:CalculateLayoutInputVertical()
end


---@class UnityEngine.UI.ScrollRect: UnityEngine.EventSystems.UIBehaviour, UnityEngine.EventSystems.IInitializePotentialDragHandler, UnityEngine.EventSystems.IBeginDragHandler, UnityEngine.EventSystems.IEndDragHandler, UnityEngine.EventSystems.IDragHandler, UnityEngine.EventSystems.IScrollHandler, UnityEngine.EventSystems.IEventSystemHandler, UnityEngine.UI.ICanvasElement, UnityEngine.UI.ILayoutElement, UnityEngine.UI.ILayoutGroup, UnityEngine.UI.ILayoutController
---@overload fun(): UnityEngine.UI.ScrollRect
local ScrollRect = {}
---@type UnityEngine.RectTransform
ScrollRect.content = nil

---@type boolean
ScrollRect.horizontal = nil

---@type boolean
ScrollRect.vertical = nil

---@type UnityEngine.UI.ScrollRect.MovementType
ScrollRect.movementType = nil

---@type number
ScrollRect.elasticity = nil

---@type boolean
ScrollRect.inertia = nil

---@type number
ScrollRect.decelerationRate = nil

---@type number
ScrollRect.scrollSensitivity = nil

---@type UnityEngine.RectTransform
ScrollRect.viewport = nil

---@type UnityEngine.UI.Scrollbar
ScrollRect.horizontalScrollbar = nil

---@type UnityEngine.UI.Scrollbar
ScrollRect.verticalScrollbar = nil

---@type UnityEngine.UI.ScrollRect.ScrollbarVisibility
ScrollRect.horizontalScrollbarVisibility = nil

---@type UnityEngine.UI.ScrollRect.ScrollbarVisibility
ScrollRect.verticalScrollbarVisibility = nil

---@type number
ScrollRect.horizontalScrollbarSpacing = nil

---@type number
ScrollRect.verticalScrollbarSpacing = nil

---@type UnityEngine.UI.ScrollRect.ScrollRectEvent
ScrollRect.onValueChanged = nil

---@type UnityEngine.Vector2
ScrollRect.velocity = nil

---@type UnityEngine.Vector2
ScrollRect.normalizedPosition = nil

---@type number
ScrollRect.horizontalNormalizedPosition = nil

---@type number
ScrollRect.verticalNormalizedPosition = nil

---@type number
ScrollRect.minWidth = nil

---@type number
ScrollRect.preferredWidth = nil

---@type number
ScrollRect.flexibleWidth = nil

---@type number
ScrollRect.minHeight = nil

---@type number
ScrollRect.preferredHeight = nil

---@type number
ScrollRect.flexibleHeight = nil

---@type integer
ScrollRect.layoutPriority = nil

---@param executing UnityEngine.UI.CanvasUpdate
---@return void
function ScrollRect:Rebuild(executing)
end

---@return void
function ScrollRect:LayoutComplete()
end

---@return void
function ScrollRect:GraphicUpdateComplete()
end

---@return boolean
function ScrollRect:IsActive()
end

---@return void
function ScrollRect:StopMovement()
end

---@param data UnityEngine.EventSystems.PointerEventData
---@return void
function ScrollRect:OnScroll(data)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function ScrollRect:OnInitializePotentialDrag(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function ScrollRect:OnBeginDrag(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function ScrollRect:OnEndDrag(eventData)
end

---@param eventData UnityEngine.EventSystems.PointerEventData
---@return void
function ScrollRect:OnDrag(eventData)
end

---@return void
function ScrollRect:CalculateLayoutInputHorizontal()
end

---@return void
function ScrollRect:CalculateLayoutInputVertical()
end

---@return void
function ScrollRect:SetLayoutHorizontal()
end

---@return void
function ScrollRect:SetLayoutVertical()
end


---游戏框架主入口 
---    统一初始化和管理所有管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#10:17"
---@class GameFramework.Core.GameFramework: UnityEngine.MonoBehaviour
---@overload fun(): GameFramework.Core.GameFramework
local GameFramework = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#35:36"
---@type GameFramework.Core.GameFramework
GameFramework.Instance = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#52:20"
---@type boolean
GameFramework.IsInitialized = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#55:40"
---@type GameFramework.Core.IGameStateManager
GameFramework.GameState = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#56:36"
---@type GameFramework.Core.ISceneManager
GameFramework.Scene = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#57:33"
---@type GameFramework.Core.IUIManager
GameFramework.UI = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#58:39"
---@type GameFramework.Core.IResourceManager
GameFramework.Resource = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#59:36"
---@type GameFramework.Core.IInputManager
GameFramework.Input = nil

---初始化框架
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#130:20"
---@return void
function GameFramework:Initialize()
end

---关闭框架
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#161:20"
---@return void
function GameFramework:Shutdown()
end

---获取管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#199:17"
---@return T
function GameFramework:GetManager()
end

---注册管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#214:20"
---@param manager T 管理器实例
---@return void
function GameFramework:RegisterManager(manager)
end

---注销管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#236:20"
---@return void
function GameFramework:UnregisterManager()
end

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#374:35"
---@param value System.Action
---@return void
function GameFramework.add_OnFrameworkInitialized(value)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#374:35"
---@param value System.Action
---@return void
function GameFramework.remove_OnFrameworkInitialized(value)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#375:35"
---@param value System.Action
---@return void
function GameFramework.add_OnFrameworkShutdown(value)
end

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameFramework.cs#375:35"
---@param value System.Action
---@return void
function GameFramework.remove_OnFrameworkShutdown(value)
end


---@interface IGameStateManager

---@interface ISceneManager

---@interface IUIManager

---@interface IResourceManager

---@interface IInputManager

---游戏状态枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#7:16"
---@enum GameFramework.Core.GameState.Detail
local GameState = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#9:8"
---@type integer
GameState.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#10:8"
---@type integer
GameState.Initialize = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#11:8"
---@type integer
GameState.Loading = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#12:8"
---@type integer
GameState.MainMenu = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#13:8"
---@type integer
GameState.InGame = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#14:8"
---@type integer
GameState.Paused = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#15:8"
---@type integer
GameState.GameOver = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#16:8"
---@type integer
GameState.Settings = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#17:8"
---@type integer
GameState.Quit = nil

---@enum (key) GameFramework.Core.GameState
---| CS.GameState.None
---| CS.GameState.Initialize
---| CS.GameState.Loading
---| CS.GameState.MainMenu
---| CS.GameState.InGame
---| CS.GameState.Paused
---| CS.GameState.GameOver
---| CS.GameState.Settings
---| CS.GameState.Quit

---UI类型枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#23:16"
---@enum GameFramework.Core.UIType.Detail
local UIType = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#25:8"
---@type integer
UIType.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#26:8"
---@type integer
UIType.FullScreen = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#27:8"
---@type integer
UIType.Popup = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#28:8"
---@type integer
UIType.Fixed = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#29:8"
---@type integer
UIType.Loading = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#30:8"
---@type integer
UIType.Dialog = nil

---@enum (key) GameFramework.Core.UIType
---| CS.UIType.None
---| CS.UIType.FullScreen
---| CS.UIType.Popup
---| CS.UIType.Fixed
---| CS.UIType.Loading
---| CS.UIType.Dialog

---UI层级枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#36:16"
---@enum GameFramework.Core.UILayer.Detail
local UILayer = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#38:8"
---@type integer
UILayer.Background = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#39:8"
---@type integer
UILayer.Normal = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#40:8"
---@type integer
UILayer.Fixed = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#41:8"
---@type integer
UILayer.Popup = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#42:8"
---@type integer
UILayer.Guide = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#43:8"
---@type integer
UILayer.Notice = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#44:8"
---@type integer
UILayer.Top = nil

---@enum (key) GameFramework.Core.UILayer
---| CS.UILayer.Background
---| CS.UILayer.Normal
---| CS.UILayer.Fixed
---| CS.UILayer.Popup
---| CS.UILayer.Guide
---| CS.UILayer.Notice
---| CS.UILayer.Top

---资源类型枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#50:16"
---@enum GameFramework.Core.ResourceType.Detail
local ResourceType = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#52:8"
---@type integer
ResourceType.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#53:8"
---@type integer
ResourceType.Prefab = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#54:8"
---@type integer
ResourceType.Scene = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#55:8"
---@type integer
ResourceType.Texture = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#56:8"
---@type integer
ResourceType.Audio = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#57:8"
---@type integer
ResourceType.Material = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#58:8"
---@type integer
ResourceType.Animation = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#59:8"
---@type integer
ResourceType.Font = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#60:8"
---@type integer
ResourceType.Shader = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#61:8"
---@type integer
ResourceType.Config = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#62:8"
---@type integer
ResourceType.Lua = nil

---@enum (key) GameFramework.Core.ResourceType
---| CS.ResourceType.None
---| CS.ResourceType.Prefab
---| CS.ResourceType.Scene
---| CS.ResourceType.Texture
---| CS.ResourceType.Audio
---| CS.ResourceType.Material
---| CS.ResourceType.Animation
---| CS.ResourceType.Font
---| CS.ResourceType.Shader
---| CS.ResourceType.Config
---| CS.ResourceType.Lua

---资源加载方式枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#68:16"
---@enum GameFramework.Core.LoadMode.Detail
local LoadMode = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#70:8"
---@type integer
LoadMode.Local = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#71:8"
---@type integer
LoadMode.Remote = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#72:8"
---@type integer
LoadMode.StreamingAssets = nil

---@enum (key) GameFramework.Core.LoadMode
---| CS.LoadMode.Local
---| CS.LoadMode.Remote
---| CS.LoadMode.StreamingAssets

---场景加载模式枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#78:16"
---@enum GameFramework.Core.SceneLoadMode.Detail
local SceneLoadMode = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#80:8"
---@type integer
SceneLoadMode.Single = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#81:8"
---@type integer
SceneLoadMode.Additive = nil

---@enum (key) GameFramework.Core.SceneLoadMode
---| CS.SceneLoadMode.Single
---| CS.SceneLoadMode.Additive

---输入设备类型枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#98:16"
---@enum GameFramework.Core.InputDeviceType.Detail
local InputDeviceType = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#100:8"
---@type integer
InputDeviceType.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#101:8"
---@type integer
InputDeviceType.KeyboardMouse = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#102:8"
---@type integer
InputDeviceType.Gamepad = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#103:8"
---@type integer
InputDeviceType.Touch = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#104:8"
---@type integer
InputDeviceType.Joystick = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#105:8"
---@type integer
InputDeviceType.XR = nil

---@enum (key) GameFramework.Core.InputDeviceType
---| CS.InputDeviceType.None
---| CS.InputDeviceType.KeyboardMouse
---| CS.InputDeviceType.Gamepad
---| CS.InputDeviceType.Touch
---| CS.InputDeviceType.Joystick
---| CS.InputDeviceType.XR

---输入动作类型枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#111:16"
---@enum GameFramework.Core.InputActionType.Detail
local InputActionType = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#113:8"
---@type integer
InputActionType.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#115:8"
---@type integer
InputActionType.Move = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#116:8"
---@type integer
InputActionType.Look = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#117:8"
---@type integer
InputActionType.Attack = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#118:8"
---@type integer
InputActionType.Interact = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#119:8"
---@type integer
InputActionType.Jump = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#120:8"
---@type integer
InputActionType.Crouch = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#121:8"
---@type integer
InputActionType.Sprint = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#122:8"
---@type integer
InputActionType.Previous = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#123:8"
---@type integer
InputActionType.Next = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#126:8"
---@type integer
InputActionType.Navigate = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#127:8"
---@type integer
InputActionType.Submit = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#128:8"
---@type integer
InputActionType.Cancel = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#129:8"
---@type integer
InputActionType.Point = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#130:8"
---@type integer
InputActionType.Click = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#131:8"
---@type integer
InputActionType.RightClick = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#132:8"
---@type integer
InputActionType.MiddleClick = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#133:8"
---@type integer
InputActionType.ScrollWheel = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#136:8"
---@type integer
InputActionType.Pause = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#137:8"
---@type integer
InputActionType.Menu = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#138:8"
---@type integer
InputActionType.Settings = nil

---@enum (key) GameFramework.Core.InputActionType
---| CS.InputActionType.None
---| CS.InputActionType.Move
---| CS.InputActionType.Look
---| CS.InputActionType.Attack
---| CS.InputActionType.Interact
---| CS.InputActionType.Jump
---| CS.InputActionType.Crouch
---| CS.InputActionType.Sprint
---| CS.InputActionType.Previous
---| CS.InputActionType.Next
---| CS.InputActionType.Navigate
---| CS.InputActionType.Submit
---| CS.InputActionType.Cancel
---| CS.InputActionType.Point
---| CS.InputActionType.Click
---| CS.InputActionType.RightClick
---| CS.InputActionType.MiddleClick
---| CS.InputActionType.ScrollWheel
---| CS.InputActionType.Pause
---| CS.InputActionType.Menu
---| CS.InputActionType.Settings

---输入状态枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#144:16"
---@enum GameFramework.Core.InputState.Detail
local InputState = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#146:8"
---@type integer
InputState.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#147:8"
---@type integer
InputState.Started = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#148:8"
---@type integer
InputState.Performed = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#149:8"
---@type integer
InputState.Canceled = nil

---@enum (key) GameFramework.Core.InputState
---| CS.InputState.None
---| CS.InputState.Started
---| CS.InputState.Performed
---| CS.InputState.Canceled

---Lua游戏框架封装 
---    为Lua提供游戏框架的主要接口
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#11:24"
---@class GameFramework.XLua.LuaGameFramework: object
---@overload fun(): GameFramework.XLua.LuaGameFramework
local LuaGameFramework = {}
---框架是否已初始化
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#16:27"
---@type boolean
LuaGameFramework.IsInitialized = nil

---游戏状态管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#21:42"
---@type GameFramework.XLua.LuaGameStateManager
LuaGameFramework.GameState = nil

---场景管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#26:38"
---@type GameFramework.XLua.LuaSceneManager
LuaGameFramework.Scene = nil

---UI管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#31:35"
---@type GameFramework.XLua.LuaUIManager
LuaGameFramework.UI = nil

---资源管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#36:41"
---@type GameFramework.XLua.LuaResourceManager
LuaGameFramework.Resource = nil

---输入管理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#41:38"
---@type GameFramework.XLua.LuaInputManager
LuaGameFramework.Input = nil

---事件系统
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#46:37"
---@type GameFramework.XLua.LuaEventSystem
LuaGameFramework.Event = nil

---初始化框架
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#51:27"
---@return void
function LuaGameFramework.Initialize()
end

---关闭框架
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#59:27"
---@return void
function LuaGameFramework.Shutdown()
end

---注册框架初始化完成回调
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#68:27"
---@param callback System.Action 回调函数
---@return void
function LuaGameFramework.OnFrameworkInitialized(callback)
end

---注册框架关闭回调
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#84:27"
---@param callback System.Action 回调函数
---@return void
function LuaGameFramework.OnFrameworkShutdown(callback)
end

---日志输出
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#93:27"
---@param message string 消息
---@return void
function LuaGameFramework.Log(message)
end

---警告日志输出
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#102:27"
---@param message string 消息
---@return void
function LuaGameFramework.LogWarning(message)
end

---错误日志输出
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#111:27"
---@param message string 消息
---@return void
function LuaGameFramework.LogError(message)
end

---获取当前时间
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#120:28"
---@return number
function LuaGameFramework.GetTime()
end

---获取增量时间
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#129:28"
---@return number
function LuaGameFramework.GetDeltaTime()
end

---获取固定增量时间
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#138:28"
---@return number
function LuaGameFramework.GetFixedDeltaTime()
end

---获取不受时间缩放影响的时间
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#147:28"
---@return number
function LuaGameFramework.GetUnscaledTime()
end

---获取不受时间缩放影响的增量时间
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#156:28"
---@return number
function LuaGameFramework.GetUnscaledDeltaTime()
end

---设置时间缩放
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#165:27"
---@param scale number 时间缩放值
---@return void
function LuaGameFramework.SetTimeScale(scale)
end

---获取时间缩放
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#174:28"
---@return number
function LuaGameFramework.GetTimeScale()
end

---暂停游戏
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#182:27"
---@return void
function LuaGameFramework.PauseGame()
end

---恢复游戏
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#190:27"
---@return void
function LuaGameFramework.ResumeGame()
end

---退出应用程序
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#198:27"
---@return void
function LuaGameFramework.QuitApplication()
end

---获取平台信息
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#207:29"
---@return string
function LuaGameFramework.GetPlatform()
end

---获取应用程序版本
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#216:29"
---@return string
function LuaGameFramework.GetVersion()
end

---获取数据路径
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#225:29"
---@return string
function LuaGameFramework.GetDataPath()
end

---获取持久化数据路径
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#234:29"
---@return string
function LuaGameFramework.GetPersistentDataPath()
end

---获取临时缓存路径
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#243:29"
---@return string
function LuaGameFramework.GetTemporaryCachePath()
end

---获取StreamingAssets路径
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#252:29"
---@return string
function LuaGameFramework.GetStreamingAssetsPath()
end

---获取屏幕宽度
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#261:26"
---@return integer
function LuaGameFramework.GetScreenWidth()
end

---获取屏幕高度
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#270:26"
---@return integer
function LuaGameFramework.GetScreenHeight()
end

---获取屏幕DPI
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#279:28"
---@return number
function LuaGameFramework.GetScreenDPI()
end

---设置目标帧率
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#288:27"
---@param frameRate integer 目标帧率
---@return void
function LuaGameFramework.SetTargetFrameRate(frameRate)
end

---获取当前帧率
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#297:28"
---@return number
function LuaGameFramework.GetFrameRate()
end

---生成随机数
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#308:28"
---@param min number 最小值
---@param max number 最大值
---@return number
function LuaGameFramework.Random(min, max)
end

---生成随机整数
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#319:26"
---@param min integer 最小值
---@param max integer 最大值
---@return integer
function LuaGameFramework.RandomInt(min, max)
end

---设置随机种子
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameFramework.cs#328:27"
---@param seed integer 种子值
---@return void
function LuaGameFramework.SetRandomSeed(seed)
end


---Lua游戏状态管理器封装
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#11:17"
---@class GameFramework.XLua.LuaGameStateManager: object
---@overload fun(): GameFramework.XLua.LuaGameStateManager
local LuaGameStateManager = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#16:42"
---@type GameFramework.XLua.LuaGameStateManager
LuaGameStateManager.Instance = nil

---当前游戏状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#31:25"
---@type GameFramework.Core.GameState
LuaGameStateManager.CurrentState = nil

---上一个游戏状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#42:25"
---@type GameFramework.Core.GameState
LuaGameStateManager.PreviousState = nil

---切换游戏状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#54:20"
---@param newState GameFramework.Core.GameState 新状态
---@return void
function LuaGameStateManager:ChangeState(newState)
end

---返回上一个状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#62:20"
---@return void
function LuaGameStateManager:ReturnToPreviousState()
end

---注册Lua状态处理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#72:20"
---@param state GameFramework.Core.GameState 游戏状态
---@param handler GameFramework.XLua.ILuaGameState Lua状态处理器
---@return void
function LuaGameStateManager:RegisterLuaStateHandler(state, handler)
end

---注销Lua状态处理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#92:20"
---@param state GameFramework.Core.GameState 游戏状态
---@return void
function LuaGameStateManager:UnregisterLuaStateHandler(state)
end

---注册状态改变事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#106:20"
---@param callback System.Action<GameFramework.Core.GameState, GameFramework.Core.GameState> 回调函数
---@return void
function LuaGameStateManager:OnStateChanged(callback)
end

---注销状态改变事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#118:20"
---@param callback System.Action<GameFramework.Core.GameState, GameFramework.Core.GameState> 回调函数
---@return void
function LuaGameStateManager:OffStateChanged(callback)
end

---检查是否为指定状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#131:20"
---@param state GameFramework.Core.GameState 要检查的状态
---@return boolean
function LuaGameStateManager:IsState(state)
end

---检查是否可以切换到指定状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#141:20"
---@param state GameFramework.Core.GameState 目标状态
---@return boolean
function LuaGameStateManager:CanChangeToState(state)
end

---获取状态名称
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#152:22"
---@param state GameFramework.Core.GameState 游戏状态
---@return string
function LuaGameStateManager:GetStateName(state)
end

---获取当前状态名称
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#161:22"
---@return string
function LuaGameStateManager:GetCurrentStateName()
end

---获取上一个状态名称
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaGameStateManager.cs#170:22"
---@return string
function LuaGameStateManager:GetPreviousStateName()
end


---Lua场景管理器封装
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#11:17"
---@class GameFramework.XLua.LuaSceneManager: object
---@overload fun(): GameFramework.XLua.LuaSceneManager
local LuaSceneManager = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#15:38"
---@type GameFramework.XLua.LuaSceneManager
LuaSceneManager.Instance = nil

---当前场景名称
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#30:22"
---@type string
LuaSceneManager.CurrentSceneName = nil

---加载场景
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#43:20"
---@param sceneName string 场景名称
---@param loadMode integer 加载模式 0=Single, 1=Additive
---@return void
function LuaSceneManager:LoadScene(sceneName, loadMode)
end

---异步加载场景
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#54:20"
---@param sceneName string 场景名称
---@param loadMode integer 加载模式 0=Single, 1=Additive
---@return void
function LuaSceneManager:LoadSceneAsync(sceneName, loadMode)
end

---卸载场景
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#64:20"
---@param sceneName string 场景名称
---@return void
function LuaSceneManager:UnloadScene(sceneName)
end

---注册场景加载进度事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#73:20"
---@param callback System.Action<float> 回调函数
---@return void
function LuaSceneManager:OnSceneLoadProgress(callback)
end

---注销场景加载进度事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#85:20"
---@param callback System.Action<float> 回调函数
---@return void
function LuaSceneManager:OffSceneLoadProgress(callback)
end

---注册场景加载完成事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#97:20"
---@param callback System.Action<string> 回调函数
---@return void
function LuaSceneManager:OnSceneLoaded(callback)
end

---注销场景加载完成事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#109:20"
---@param callback System.Action<string> 回调函数
---@return void
function LuaSceneManager:OffSceneLoaded(callback)
end

---检查场景是否已加载
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#122:20"
---@param sceneName string 场景名称
---@return boolean
function LuaSceneManager:IsSceneLoaded(sceneName)
end

---获取已加载的场景列表
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#132:24"
---@return string[]
function LuaSceneManager:GetLoadedScenes()
end

---检查是否为当前场景
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaSceneManager.cs#144:20"
---@param sceneName string 场景名称
---@return boolean
function LuaSceneManager:IsCurrentScene(sceneName)
end


---Lua UI管理器封装
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#11:17"
---@class GameFramework.XLua.LuaUIManager: object
---@overload fun(): GameFramework.XLua.LuaUIManager
local LuaUIManager = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#16:35"
---@type GameFramework.XLua.LuaUIManager
LuaUIManager.Instance = nil

---创建UI
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#35:26"
---@param uiName string UI名称
---@param uiType integer UI类型 0=Normal, 1=FullScreen, 2=Popup, 3=Fixed, 4=Loading, 5=Dialog
---@param layer integer UI层级 0=Background, 1=Normal, 2=Fixed, 3=Popup, 4=Guide, 5=Notice, 6=Top
---@return UnityEngine.GameObject
function LuaUIManager:CreateUI(uiName, uiType, layer)
end

---创建UI并绑定Lua组件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#52:26"
---@param uiName string UI名称
---@param luaComponent GameFramework.XLua.ILuaUIComponent Lua UI组件
---@param uiType integer UI类型
---@param layer integer UI层级
---@return UnityEngine.GameObject
function LuaUIManager:CreateUIWithLua(uiName, luaComponent, uiType, layer)
end

---显示UI
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#79:20"
---@param uiName string UI名称
---@return void
function LuaUIManager:ShowUI(uiName)
end

---隐藏UI
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#100:20"
---@param uiName string UI名称
---@return void
function LuaUIManager:HideUI(uiName)
end

---销毁UI
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#121:20"
---@param uiName string UI名称
---@return void
function LuaUIManager:DestroyUI(uiName)
end

---获取UI GameObject
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#144:26"
---@param uiName string UI名称
---@return UnityEngine.GameObject
function LuaUIManager:GetUI(uiName)
end

---获取UI信息
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#155:22"
---@param uiName string UI名称
---@return string
function LuaUIManager:GetUIInfo(uiName)
end

---检查UI是否存在
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#172:20"
---@param uiName string UI名称
---@return boolean
function LuaUIManager:HasUI(uiName)
end

---检查UI是否可见
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#182:20"
---@param uiName string UI名称
---@return boolean
function LuaUIManager:IsUIVisible(uiName)
end

---切换UI显示状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#193:20"
---@param uiName string UI名称
---@return void
function LuaUIManager:ToggleUI(uiName)
end

---返回上一个UI
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#208:20"
---@return void
function LuaUIManager:GoBackToPreviousUI()
end

---获取Lua UI组件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#219:31"
---@param uiName string UI名称
---@return GameFramework.XLua.ILuaUIComponent
function LuaUIManager:GetLuaUIComponent(uiName)
end

---更新所有Lua UI组件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaUIManager.cs#227:20"
---@return void
function LuaUIManager:UpdateLuaUIComponents()
end


---Lua资源管理器封装
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#10:17"
---@class GameFramework.XLua.LuaResourceManager: object
---@overload fun(): GameFramework.XLua.LuaResourceManager
local LuaResourceManager = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#14:41"
---@type GameFramework.XLua.LuaResourceManager
LuaResourceManager.Instance = nil

---加载GameObject资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#32:26"
---@param path string 资源路径
---@param loadMode integer 加载模式 0=Local, 1=Remote, 2=StreamingAssets
---@return UnityEngine.GameObject
function LuaResourceManager:LoadGameObject(path, loadMode)
end

---异步加载GameObject资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#44:20"
---@param path string 资源路径
---@param callback System.Action<UnityEngine.GameObject> 加载完成回调
---@param loadMode integer 加载模式
---@return void
function LuaResourceManager:LoadGameObjectAsync(path, callback, loadMode)
end

---加载纹理资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#56:25"
---@param path string 资源路径
---@param loadMode integer 加载模式
---@return UnityEngine.Texture2D
function LuaResourceManager:LoadTexture(path, loadMode)
end

---异步加载纹理资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#68:20"
---@param path string 资源路径
---@param callback System.Action<UnityEngine.Texture2D> 加载完成回调
---@param loadMode integer 加载模式
---@return void
function LuaResourceManager:LoadTextureAsync(path, callback, loadMode)
end

---加载音频资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#80:25"
---@param path string 资源路径
---@param loadMode integer 加载模式
---@return UnityEngine.AudioClip
function LuaResourceManager:LoadAudio(path, loadMode)
end

---异步加载音频资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#92:20"
---@param path string 资源路径
---@param callback System.Action<UnityEngine.AudioClip> 加载完成回调
---@param loadMode integer 加载模式
---@return void
function LuaResourceManager:LoadAudioAsync(path, callback, loadMode)
end

---加载文本资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#104:25"
---@param path string 资源路径
---@param loadMode integer 加载模式
---@return UnityEngine.TextAsset
function LuaResourceManager:LoadText(path, loadMode)
end

---异步加载文本资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#116:20"
---@param path string 资源路径
---@param callback System.Action<UnityEngine.TextAsset> 加载完成回调
---@param loadMode integer 加载模式
---@return void
function LuaResourceManager:LoadTextAsync(path, callback, loadMode)
end

---卸载资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#126:20"
---@param path string 资源路径
---@return void
function LuaResourceManager:UnloadResource(path)
end

---下载远程资源
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#138:20"
---@param url string 下载地址
---@param savePath string 保存路径
---@param onProgress System.Action<float> 下载进度回调
---@param onComplete System.Action<bool> 下载完成回调
---@return void
function LuaResourceManager:DownloadResource(url, savePath, onProgress, onComplete)
end

---实例化GameObject
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#150:26"
---@param path string 预制体路径
---@param parent UnityEngine.Transform 父对象
---@param loadMode integer 加载模式
---@return UnityEngine.GameObject
function LuaResourceManager:Instantiate(path, parent, loadMode)
end

---异步实例化GameObject
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#167:20"
---@param path string 预制体路径
---@param callback System.Action<UnityEngine.GameObject> 实例化完成回调
---@param parent UnityEngine.Transform 父对象
---@param loadMode integer 加载模式
---@return void
function LuaResourceManager:InstantiateAsync(path, callback, parent, loadMode)
end

---获取资源信息
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#188:22"
---@param path string 资源路径
---@return string
function LuaResourceManager:GetResourceInfo(path)
end

---获取已加载资源列表
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#204:24"
---@return string[]
function LuaResourceManager:GetLoadedResources()
end

---检查资源是否已加载
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaResourceManager.cs#216:20"
---@param path string 资源路径
---@return boolean
function LuaResourceManager:IsResourceLoaded(path)
end


---Lua输入管理器封装
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#11:17"
---@class GameFramework.XLua.LuaInputManager: object
---@overload fun(): GameFramework.XLua.LuaInputManager
local LuaInputManager = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#16:38"
---@type GameFramework.XLua.LuaInputManager
LuaInputManager.Instance = nil

---当前输入设备类型
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#31:19"
---@type integer
LuaInputManager.CurrentDeviceType = nil

---输入是否启用
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#42:20"
---@type boolean
LuaInputManager.InputEnabled = nil

---获取按钮输入状态
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#62:20"
---@param actionType integer 动作类型
---@return boolean
function LuaInputManager:GetButton(actionType)
end

---获取按钮按下事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#73:20"
---@param actionType integer 动作类型
---@return boolean
function LuaInputManager:GetButtonDown(actionType)
end

---获取按钮释放事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#84:20"
---@param actionType integer 动作类型
---@return boolean
function LuaInputManager:GetButtonUp(actionType)
end

---获取轴输入值
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#95:21"
---@param actionType integer 动作类型
---@return number
function LuaInputManager:GetAxis(actionType)
end

---获取向量输入值
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#106:23"
---@param actionType integer 动作类型
---@return UnityEngine.Vector2
function LuaInputManager:GetVector2(actionType)
end

---启用输入动作映射
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#116:20"
---@param mapName string 映射名称
---@return void
function LuaInputManager:EnableActionMap(mapName)
end

---禁用输入动作映射
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#125:20"
---@param mapName string 映射名称
---@return void
function LuaInputManager:DisableActionMap(mapName)
end

---切换输入动作映射
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#134:20"
---@param mapName string 映射名称
---@return void
function LuaInputManager:SwitchActionMap(mapName)
end

---注册Lua输入处理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#144:20"
---@param handlerName string 处理器名称
---@param handler GameFramework.XLua.ILuaInputHandler Lua输入处理器
---@return void
function LuaInputManager:RegisterLuaInputHandler(handlerName, handler)
end

---注销Lua输入处理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#159:20"
---@param handlerName string 处理器名称
---@return void
function LuaInputManager:UnregisterLuaInputHandler(handlerName)
end

---注册输入动作事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#172:20"
---@param callback System.Action<GameFramework.Core.InputActionType, GameFramework.Core.InputState, object> 回调函数
---@return void
function LuaInputManager:OnInputAction(callback)
end

---注销输入动作事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#184:20"
---@param callback System.Action<GameFramework.Core.InputActionType, GameFramework.Core.InputState, object> 回调函数
---@return void
function LuaInputManager:OffInputAction(callback)
end

---注册设备切换事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#196:20"
---@param callback System.Action<GameFramework.Core.InputDeviceType> 回调函数
---@return void
function LuaInputManager:OnDeviceChanged(callback)
end

---注销设备切换事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#208:20"
---@param callback System.Action<GameFramework.Core.InputDeviceType> 回调函数
---@return void
function LuaInputManager:OffDeviceChanged(callback)
end

---分发输入事件到Lua处理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#222:20"
---@param actionType GameFramework.Core.InputActionType 动作类型
---@param state GameFramework.Core.InputState 输入状态
---@param value table 输入值
---@return void
function LuaInputManager:DispatchInputToLua(actionType, state, value)
end

---分发设备切换事件到Lua处理器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#241:20"
---@param deviceType GameFramework.Core.InputDeviceType 设备类型
---@return void
function LuaInputManager:DispatchDeviceChangeToLua(deviceType)
end

---获取设备类型名称
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#261:22"
---@param deviceType integer 设备类型
---@return string
function LuaInputManager:GetDeviceTypeName(deviceType)
end

---获取动作类型名称
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#272:22"
---@param actionType integer 动作类型
---@return string
function LuaInputManager:GetActionTypeName(actionType)
end

---检查是否为指定设备类型
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaInputManager.cs#283:20"
---@param deviceType integer 设备类型
---@return boolean
function LuaInputManager:IsDeviceType(deviceType)
end


---Lua事件系统封装 
---    提供Lua脚本之间的事件通信机制
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#11:17"
---@class GameFramework.XLua.LuaEventSystem: object
---@overload fun(): GameFramework.XLua.LuaEventSystem
local LuaEventSystem = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#17:37"
---@type GameFramework.XLua.LuaEventSystem
LuaEventSystem.Instance = nil

---注册事件监听器（带参数）
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#34:20"
---@param eventName string 事件名称
---@param listener System.Action<object> 监听器
---@return void
function LuaEventSystem:AddListener(eventName, listener)
end

---注册事件监听器（无参数）
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#56:20"
---@param eventName string 事件名称
---@param listener System.Action 监听器
---@return void
function LuaEventSystem:AddSimpleListener(eventName, listener)
end

---移除事件监听器（带参数）
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#78:20"
---@param eventName string 事件名称
---@param listener System.Action<object> 监听器
---@return void
function LuaEventSystem:RemoveListener(eventName, listener)
end

---移除事件监听器（无参数）
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#99:20"
---@param eventName string 事件名称
---@param listener System.Action 监听器
---@return void
function LuaEventSystem:RemoveSimpleListener(eventName, listener)
end

---触发事件（带参数）
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#120:20"
---@param eventName string 事件名称
---@param data table 事件数据
---@return void
function LuaEventSystem:TriggerEvent(eventName, data)
end

---触发事件（无参数）
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#148:20"
---@param eventName string 事件名称
---@return void
function LuaEventSystem:TriggerSimpleEvent(eventName)
end

---移除指定事件的所有监听器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#176:20"
---@param eventName string 事件名称
---@return void
function LuaEventSystem:RemoveAllListeners(eventName)
end

---清除所有事件监听器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#197:20"
---@return void
function LuaEventSystem:ClearAllListeners()
end

---检查事件是否有监听器
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#209:20"
---@param eventName string 事件名称
---@return boolean
function LuaEventSystem:HasListeners(eventName)
end

---获取事件监听器数量
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#223:19"
---@param eventName string 事件名称
---@return integer
function LuaEventSystem:GetListenerCount(eventName)
end

---获取所有事件名称
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#244:24"
---@return string[]
function LuaEventSystem:GetAllEventNames()
end

---延迟触发事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#264:20"
---@param eventName string 事件名称
---@param delay number 延迟时间（秒）
---@param data table 事件数据
---@return void
function LuaEventSystem:TriggerEventDelayed(eventName, delay, data)
end

---延迟触发简单事件
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/XLua/LuaEventSystem.cs#280:20"
---@param eventName string 事件名称
---@param delay number 延迟时间（秒）
---@return void
function LuaEventSystem:TriggerSimpleEventDelayed(eventName, delay)
end


---@alias Action fun(): void

---游戏状态枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#7:16"
---@enum GameFramework.Core.GameState.Detail
local GameState = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#9:8"
---@type integer
GameState.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#10:8"
---@type integer
GameState.Initialize = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#11:8"
---@type integer
GameState.Loading = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#12:8"
---@type integer
GameState.MainMenu = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#13:8"
---@type integer
GameState.InGame = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#14:8"
---@type integer
GameState.Paused = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#15:8"
---@type integer
GameState.GameOver = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#16:8"
---@type integer
GameState.Settings = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#17:8"
---@type integer
GameState.Quit = nil

---@enum (key) GameFramework.Core.GameState
---| CS.GameState.None
---| CS.GameState.Initialize
---| CS.GameState.Loading
---| CS.GameState.MainMenu
---| CS.GameState.InGame
---| CS.GameState.Paused
---| CS.GameState.GameOver
---| CS.GameState.Settings
---| CS.GameState.Quit

---游戏状态枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#7:16"
---@enum GameFramework.Core.GameState.Detail
local GameState = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#9:8"
---@type integer
GameState.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#10:8"
---@type integer
GameState.Initialize = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#11:8"
---@type integer
GameState.Loading = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#12:8"
---@type integer
GameState.MainMenu = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#13:8"
---@type integer
GameState.InGame = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#14:8"
---@type integer
GameState.Paused = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#15:8"
---@type integer
GameState.GameOver = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#16:8"
---@type integer
GameState.Settings = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#17:8"
---@type integer
GameState.Quit = nil

---@enum (key) GameFramework.Core.GameState
---| CS.GameState.None
---| CS.GameState.Initialize
---| CS.GameState.Loading
---| CS.GameState.MainMenu
---| CS.GameState.InGame
---| CS.GameState.Paused
---| CS.GameState.GameOver
---| CS.GameState.Settings
---| CS.GameState.Quit

---输入动作类型枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#111:16"
---@enum GameFramework.Core.InputActionType.Detail
local InputActionType = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#113:8"
---@type integer
InputActionType.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#115:8"
---@type integer
InputActionType.Move = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#116:8"
---@type integer
InputActionType.Look = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#117:8"
---@type integer
InputActionType.Attack = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#118:8"
---@type integer
InputActionType.Interact = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#119:8"
---@type integer
InputActionType.Jump = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#120:8"
---@type integer
InputActionType.Crouch = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#121:8"
---@type integer
InputActionType.Sprint = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#122:8"
---@type integer
InputActionType.Previous = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#123:8"
---@type integer
InputActionType.Next = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#126:8"
---@type integer
InputActionType.Navigate = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#127:8"
---@type integer
InputActionType.Submit = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#128:8"
---@type integer
InputActionType.Cancel = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#129:8"
---@type integer
InputActionType.Point = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#130:8"
---@type integer
InputActionType.Click = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#131:8"
---@type integer
InputActionType.RightClick = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#132:8"
---@type integer
InputActionType.MiddleClick = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#133:8"
---@type integer
InputActionType.ScrollWheel = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#136:8"
---@type integer
InputActionType.Pause = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#137:8"
---@type integer
InputActionType.Menu = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#138:8"
---@type integer
InputActionType.Settings = nil

---@enum (key) GameFramework.Core.InputActionType
---| CS.InputActionType.None
---| CS.InputActionType.Move
---| CS.InputActionType.Look
---| CS.InputActionType.Attack
---| CS.InputActionType.Interact
---| CS.InputActionType.Jump
---| CS.InputActionType.Crouch
---| CS.InputActionType.Sprint
---| CS.InputActionType.Previous
---| CS.InputActionType.Next
---| CS.InputActionType.Navigate
---| CS.InputActionType.Submit
---| CS.InputActionType.Cancel
---| CS.InputActionType.Point
---| CS.InputActionType.Click
---| CS.InputActionType.RightClick
---| CS.InputActionType.MiddleClick
---| CS.InputActionType.ScrollWheel
---| CS.InputActionType.Pause
---| CS.InputActionType.Menu
---| CS.InputActionType.Settings

---输入状态枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#144:16"
---@enum GameFramework.Core.InputState.Detail
local InputState = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#146:8"
---@type integer
InputState.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#147:8"
---@type integer
InputState.Started = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#148:8"
---@type integer
InputState.Performed = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#149:8"
---@type integer
InputState.Canceled = nil

---@enum (key) GameFramework.Core.InputState
---| CS.InputState.None
---| CS.InputState.Started
---| CS.InputState.Performed
---| CS.InputState.Canceled

---输入设备类型枚举
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#98:16"
---@enum GameFramework.Core.InputDeviceType.Detail
local InputDeviceType = {}
---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#100:8"
---@type integer
InputDeviceType.None = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#101:8"
---@type integer
InputDeviceType.KeyboardMouse = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#102:8"
---@type integer
InputDeviceType.Gamepad = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#103:8"
---@type integer
InputDeviceType.Touch = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#104:8"
---@type integer
InputDeviceType.Joystick = nil

---@source "file:///f:/Unity/Unity6/My project/Assets/Script/Core/GameEnums.cs#105:8"
---@type integer
InputDeviceType.XR = nil

---@enum (key) GameFramework.Core.InputDeviceType
---| CS.InputDeviceType.None
---| CS.InputDeviceType.KeyboardMouse
---| CS.InputDeviceType.Gamepad
---| CS.InputDeviceType.Touch
---| CS.InputDeviceType.Joystick
---| CS.InputDeviceType.XR

---@alias UnityAction fun(): void

