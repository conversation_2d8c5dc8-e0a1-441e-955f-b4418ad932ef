﻿local MainUI = {}

-- 存储GameObject引用
MainUI.gameObject = nil
MainUI.transform = nil

-- 资源路径
local PREFAB_PATH = "UI/MainUI"

-- 加载prefab并实例化
function MainUI:LoadPrefab()
    local prefab = CS.UnityEngine.Resources.Load(PREFAB_PATH)
    if prefab then
        self.gameObject = CS.UnityEngine.Object.Instantiate(prefab)
        self.transform = self.gameObject.transform
        print("MainUI: Prefab loaded successfully")
        return self.gameObject
    else
        print("MainUI: Failed to load prefab from path: " .. PREFAB_PATH)
        return nil
    end
end

function MainUI:OnCreate(gameObject)
    print("MainUI: OnCreate called")

    -- 如果没有传入gameObject，则加载prefab
    if not gameObject then
        gameObject = self:LoadPrefab()
    end

    -- 存储GameObject引用供其他方法使用
    self.gameObject = gameObject
    if gameObject then
        self.transform = gameObject.transform
        print("MainUI: GameObject name = " .. gameObject.name)
    else
        print("MainUI: GameObject is nil!")
        return
    end

    -- 在这里获取UI组件引用
    self:InitComponents()
end

function MainUI:InitComponents()
    -- 获取UI组件引用
    -- 例如:
    -- self.button = self.transform:Find("Button"):GetComponent(typeof(CS.UnityEngine.UI.Button))
    -- self.text = self.transform:Find("Text"):GetComponent(typeof(CS.UnityEngine.UI.Text))
    -- self.image = self.transform:Find("Image"):GetComponent(typeof(CS.UnityEngine.UI.Image))
end

function MainUI:OnShow()
    print("MainUI: OnShow")
    if self.gameObject then
        self.gameObject:SetActive(true)
    end
end

function MainUI:OnHide()
    print("MainUI: OnHide")
    if self.gameObject then
        self.gameObject:SetActive(false)
    end
end

function MainUI:OnDestroy()
    print("MainUI: OnDestroy")
    if self.gameObject then
        CS.UnityEngine.Object.Destroy(self.gameObject)
        self.gameObject = nil
        self.transform = nil
    end
end

function MainUI:OnUpdate()
    -- UI更新逻辑
end

return MainUI
