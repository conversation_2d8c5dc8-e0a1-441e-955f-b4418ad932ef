local MainUI = {}

-- 存储GameObject引用
MainUI.gameObject = nil

function MainUI:OnCreate(gameObject)
    print("MainUI: OnCreate called with gameObject =", gameObject)

    -- 存储GameObject引用供其他方法使用
    self.gameObject = gameObject

    if gameObject then
        print("MainUI: GameObject name =", gameObject.name)
        print("MainUI: GameObject tag =", gameObject.tag)
    else
        print("MainUI: GameObject is nil!")
    end
end

function MainUI:OnShow()
    print("MainUI OnShow")
end

function MainUI:OnHide()
    print("MainUI OnHide")
end

function MainUI:OnDestroy()
    print("MainUI OnDestroy")
end

function MainUI:OnUpdate()
    -- UI更新逻辑
end

return MainUI
