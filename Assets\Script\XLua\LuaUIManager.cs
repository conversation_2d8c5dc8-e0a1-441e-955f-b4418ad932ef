using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;
using GameFramework.Core;

namespace GameFramework.XLua
{
    /// <summary>
    /// Lua UI管理器封装
    /// </summary>
    public class LuaUIManager
    {
        private static LuaUIManager instance;
        private Dictionary<string, ILuaUIComponent> luaUIComponents = new Dictionary<string, ILuaUIComponent>();

        public static LuaUIManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LuaUIManager();
                }
                return instance;
            }
        }

        /// <summary>
        /// 创建UI
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <param name="uiType">UI类型 0=Normal, 1=FullScreen, 2=Popup, 3=Fixed, 4=Loading, 5=Dialog</param>
        /// <param name="layer">UI层级 0=Background, 1=Normal, 2=Fixed, 3=Popup, 4=Guide, 5=Notice, 6=Top</param>
        /// <returns>UI GameObject</returns>
        public GameObject CreateUI(string uiName, int uiType = 1, int layer = 1)
        {
            UIType type = (UIType)uiType;
            UILayer uiLayer = (UILayer)layer;

            var ui = GameFramework.Core.GameFramework.UI?.CreateUI<LuaUIComponent>(uiName, type, uiLayer);
            if (ui != null)
            {
                ui.UIName = uiName;
                return ui.gameObject;
            }
            return null;
        }

        /// <summary>
        /// 创建UI并绑定Lua组件
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <param name="luaComponent">Lua UI组件</param>
        /// <param name="uiType">UI类型</param>
        /// <param name="layer">UI层级</param>
        /// <returns>UI GameObject</returns>
        public GameObject CreateUIWithLua(string uiName, ILuaUIComponent luaComponent, int uiType = 1, int layer = 1)
        {
            GameObject uiObject = CreateUI(uiName, uiType, layer);
            if (uiObject != null && luaComponent != null)
            {
                luaUIComponents[uiName] = luaComponent;
                
                // 添加Lua UI包装组件
                var wrapper = uiObject.AddComponent<LuaUIWrapper>();
                wrapper.SetLuaComponent(luaComponent);
                
                try
                {
                    Debug.Log($"[LuaUIManager] 调用Lua UI组件OnCreate: {uiName}");
                    luaComponent.OnCreate(uiObject);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaUIManager] Lua UI组件OnCreate异常: {e.Message}");
                }
            }
            return uiObject;
        }

        /// <summary>
        /// 显示UI
        /// </summary>
        /// <param name="uiName">UI名称</param>
        public void ShowUI(string uiName)
        {
            GameFramework.Core.GameFramework.UI?.ShowUI(uiName);
            
            if (luaUIComponents.ContainsKey(uiName))
            {
                try
                {
                    luaUIComponents[uiName].OnShow();
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaUIManager] Lua UI组件OnShow异常: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 隐藏UI
        /// </summary>
        /// <param name="uiName">UI名称</param>
        public void HideUI(string uiName)
        {
            GameFramework.Core.GameFramework.UI?.HideUI(uiName);
            
            if (luaUIComponents.ContainsKey(uiName))
            {
                try
                {
                    luaUIComponents[uiName].OnHide();
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaUIManager] Lua UI组件OnHide异常: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 销毁UI
        /// </summary>
        /// <param name="uiName">UI名称</param>
        public void DestroyUI(string uiName)
        {
            if (luaUIComponents.ContainsKey(uiName))
            {
                try
                {
                    luaUIComponents[uiName].OnDestroy();
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaUIManager] Lua UI组件OnDestroy异常: {e.Message}");
                }
                luaUIComponents.Remove(uiName);
            }
            
            GameFramework.Core.GameFramework.UI?.DestroyUI(uiName);
        }

        /// <summary>
        /// 获取UI GameObject
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <returns>UI GameObject</returns>
        public GameObject GetUI(string uiName)
        {
            var ui = GameFramework.Core.GameFramework.UI?.GetUI<MonoBehaviour>(uiName);
            return ui?.gameObject;
        }

        /// <summary>
        /// 获取UI信息
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <returns>UI信息字符串</returns>
        public string GetUIInfo(string uiName)
        {
            var uiManager = GameFramework.Core.GameFramework.UI as GameFramework.Core.UIManager;
            var uiInfo = uiManager?.GetUIInfo(uiName);
            
            if (uiInfo != null)
            {
                return $"名称:{uiInfo.name}, 类型:{uiInfo.type}, 层级:{uiInfo.layer}, 可见:{uiInfo.isVisible}";
            }
            return "UI不存在";
        }

        /// <summary>
        /// 检查UI是否存在
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <returns>是否存在</returns>
        public bool HasUI(string uiName)
        {
            return GetUI(uiName) != null;
        }

        /// <summary>
        /// 检查UI是否可见
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <returns>是否可见</returns>
        public bool IsUIVisible(string uiName)
        {
            var uiManager = GameFramework.Core.GameFramework.UI as GameFramework.Core.UIManager;
            var uiInfo = uiManager?.GetUIInfo(uiName);
            return uiInfo?.isVisible ?? false;
        }

        /// <summary>
        /// 切换UI显示状态
        /// </summary>
        /// <param name="uiName">UI名称</param>
        public void ToggleUI(string uiName)
        {
            if (IsUIVisible(uiName))
            {
                HideUI(uiName);
            }
            else
            {
                ShowUI(uiName);
            }
        }

        /// <summary>
        /// 返回上一个UI
        /// </summary>
        public void GoBackToPreviousUI()
        {
            var uiManager = GameFramework.Core.GameFramework.UI as GameFramework.Core.UIManager;
            uiManager?.GoBackToPreviousUI();
        }

        /// <summary>
        /// 获取Lua UI组件
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <returns>Lua UI组件</returns>
        public ILuaUIComponent GetLuaUIComponent(string uiName)
        {
            return luaUIComponents.ContainsKey(uiName) ? luaUIComponents[uiName] : null;
        }

        /// <summary>
        /// 更新所有Lua UI组件
        /// </summary>
        public void UpdateLuaUIComponents()
        {
            foreach (var component in luaUIComponents.Values)
            {
                try
                {
                    component.OnUpdate();
                }
                catch (Exception e)
                {
                    Debug.LogError($"[LuaUIManager] Lua UI组件OnUpdate异常: {e.Message}");
                }
            }
        }
    }

    /// <summary>
    /// Lua UI包装组件
    /// 将Lua UI组件包装为Unity组件
    /// </summary>
    public class LuaUIWrapper : MonoBehaviour
    {
        private ILuaUIComponent luaComponent;

        public void SetLuaComponent(ILuaUIComponent component)
        {
            luaComponent = component;
        }

        private void Update()
        {
            try
            {
                luaComponent?.OnUpdate();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaUIWrapper] OnUpdate异常: {e.Message}");
            }
        }

        private void OnDestroy()
        {
            try
            {
                luaComponent?.OnDestroy();
            }
            catch (Exception e)
            {
                Debug.LogError($"[LuaUIWrapper] OnDestroy异常: {e.Message}");
            }
        }
    }

    /// <summary>
    /// Lua UI辅助类
    /// 提供一些便捷的UI操作方法
    /// </summary>
    public static class LuaUIHelper
    {
        /// <summary>
        /// 创建主菜单UI
        /// </summary>
        /// <param name="luaComponent">Lua组件</param>
        /// <returns>UI GameObject</returns>
        public static GameObject CreateMainMenuUI(ILuaUIComponent luaComponent = null)
        {
            return LuaGameFramework.UI.CreateUIWithLua("MainMenu", luaComponent, 1, 1); // FullScreen, Normal
        }

        /// <summary>
        /// 创建游戏HUD UI
        /// </summary>
        /// <param name="luaComponent">Lua组件</param>
        /// <returns>UI GameObject</returns>
        public static GameObject CreateGameHUD(ILuaUIComponent luaComponent = null)
        {
            return LuaGameFramework.UI.CreateUIWithLua("GameHUD", luaComponent, 3, 2); // Fixed, Fixed
        }

        /// <summary>
        /// 创建弹出对话框
        /// </summary>
        /// <param name="dialogName">对话框名称</param>
        /// <param name="luaComponent">Lua组件</param>
        /// <returns>UI GameObject</returns>
        public static GameObject CreateDialog(string dialogName, ILuaUIComponent luaComponent = null)
        {
            return LuaGameFramework.UI.CreateUIWithLua(dialogName, luaComponent, 5, 3); // Dialog, Popup
        }

        /// <summary>
        /// 创建加载界面
        /// </summary>
        /// <param name="luaComponent">Lua组件</param>
        /// <returns>UI GameObject</returns>
        public static GameObject CreateLoadingUI(ILuaUIComponent luaComponent = null)
        {
            return LuaGameFramework.UI.CreateUIWithLua("Loading", luaComponent, 4, 6); // Loading, Top
        }

        /// <summary>
        /// 显示消息提示
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="duration">显示时长</param>
        public static void ShowMessage(string message, float duration = 3f)
        {
            // 这里可以创建一个简单的消息提示UI
            Debug.Log($"[Message] {message}");
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="content">内容</param>
        /// <param name="onConfirm">确认回调</param>
        /// <param name="onCancel">取消回调</param>
        public static void ShowConfirmDialog(string title, string content, System.Action onConfirm = null, System.Action onCancel = null)
        {
            // 这里可以创建确认对话框的逻辑
            Debug.Log($"[ConfirmDialog] {title}: {content}");
        }

        /// <summary>
        /// 关闭所有弹出UI
        /// </summary>
        public static void CloseAllPopups()
        {
            // 这里可以添加关闭所有弹出UI的逻辑
            Debug.Log("[LuaUIHelper] 关闭所有弹出UI");
        }

        /// <summary>
        /// 设置UI可见性
        /// </summary>
        /// <param name="uiName">UI名称</param>
        /// <param name="visible">是否可见</param>
        public static void SetUIVisible(string uiName, bool visible)
        {
            if (visible)
            {
                LuaGameFramework.UI.ShowUI(uiName);
            }
            else
            {
                LuaGameFramework.UI.HideUI(uiName);
            }
        }

        /// <summary>
        /// 批量显示UI
        /// </summary>
        /// <param name="uiNames">UI名称数组</param>
        public static void ShowUIs(string[] uiNames)
        {
            foreach (string uiName in uiNames)
            {
                LuaGameFramework.UI.ShowUI(uiName);
            }
        }

        /// <summary>
        /// 批量隐藏UI
        /// </summary>
        /// <param name="uiNames">UI名称数组</param>
        public static void HideUIs(string[] uiNames)
        {
            foreach (string uiName in uiNames)
            {
                LuaGameFramework.UI.HideUI(uiName);
            }
        }
    }
}
