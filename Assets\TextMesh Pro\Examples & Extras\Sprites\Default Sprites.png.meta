fileFormatVersion: 2
guid: a0fc465d6cf04254a2938578735e2383
TextureImporter:
  fileIDToRecycleName:
    21300000: Default Sprite Asset_0
    21300002: Default Sprite Asset_1
    21300004: Default Sprite Asset_2
    21300006: Default Sprite Asset_3
    21300008: Default Sprite Asset_4
    21300010: Default Sprite Asset_5
    21300012: Default Sprite Asset_6
    21300014: Default Sprite Asset_7
    21300016: Default Sprite Asset_8
    21300018: Default Sprite Asset_11
    21300020: Default Sprite Asset_12
    21300022: Default Sprite Asset_13
    21300024: Default Sprite Asset_14
    21300026: Default Sprite Asset_15
  externalObjects: {}
  serializedVersion: 7
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: -1
  maxTextureSize: 512
  textureSettings:
    serializedVersion: 2
    filterMode: -1
    aniso: 16
    mipBias: -100
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 0
  textureShape: 1
  singleChannelComponent: 0
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  platformSettings:
  - serializedVersion: 2
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: iPhone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: Android
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: Windows Store Apps
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: WebGL
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Default Sprite Asset_0
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a09de5b73098b2247921a5d8566535e9
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_1
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f23aaeaabde92743b6e764e7373d24e
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_2
      rect:
        serializedVersion: 2
        x: 256
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7a53700b4f8ee664dbc6d96f6897dcca
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_3
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8aee68303a75a5240a3bfb6295e83c8d
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_4
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62b55056d79f32242863dd4585a55f00
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_5
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40e7bb1e441a02942a5912eb1bd6beb0
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_6
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 264af04cf7ba9a6499d6605e8546b922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_7
      rect:
        serializedVersion: 2
        x: 384
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5df87e7d9c18ccf4eab4728947f9d93c
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_8
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7670512dd461b8940a38efcf24a8081d
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_11
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 384
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d2f6b0f97da03b43a92ce0dc86fc195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c22fab65c0b72cf43bad23754277ed43
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_13
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e669cb3dece225c4c80a6260a92e44a4
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_14
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc45a1c7b06b45d43aec543c9d13ac11
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Default Sprite Asset_15
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 81d0fca21c1e3dd46889168e33cda991
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 
    vertices: []
    indices: 
    edges: []
    weights: []
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
