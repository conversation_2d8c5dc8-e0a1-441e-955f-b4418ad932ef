using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text;
using GameFramework.XLua;
using System.Reflection;

[CustomEditor(typeof(UIComponentCollector))]
public class UIComponentCollectorEditor : Editor
{
    private UIComponentCollector collector;
    
    private void OnEnable()
    {
        collector = (UIComponentCollector)target;
    }
    
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("组件收集器操作", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("清空收集列表"))
        {
            if (EditorUtility.DisplayDialog("确认", "确定要清空所有收集的组件吗？", "确定", "取消"))
            {
                collector.ClearComponents();
                EditorUtility.SetDirty(collector);
            }
        }
        
        if (GUILayout.Button("验证组件"))
        {
            collector.ValidateComponents();
            EditorUtility.SetDirty(collector);
        }
        EditorGUILayout.EndHorizontal();
        
        if (GUILayout.Button("生成Lua绑定代码"))
        {
            GenerateLuaBinding();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField($"已收集组件数量: {collector.CollectedComponents.Count}");
    }
    
    private void GenerateLuaBinding()
    {
        string prefabName = collector.gameObject.name;
        string luaCode = collector.GenerateLuaBindingCode(prefabName);
        
        // 显示生成的代码
        var window = EditorWindow.GetWindow<LuaBindingCodeWindow>("Lua绑定代码");
        window.SetCode(luaCode, prefabName);
        window.Show();
    }
}

public class LuaBindingCodeWindow : EditorWindow
{
    private string code = "";
    private string className = "";
    private Vector2 scrollPosition;
    
    public void SetCode(string code, string className)
    {
        this.code = code;
        this.className = className;
    }
    
    private void OnGUI()
    {
        EditorGUILayout.LabelField($"Lua绑定代码 - {className}", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("复制到剪贴板"))
        {
            EditorGUIUtility.systemCopyBuffer = code;
            ShowNotification(new GUIContent("已复制到剪贴板"));
        }
        
        if (GUILayout.Button("更新到Lua文件"))
        {
            UpdateLuaFile();
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        EditorGUILayout.TextArea(code, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
    }
    
    private void UpdateLuaFile()
    {
        string luaFilePath = Path.Combine("Assets/Resources/LuaScripts", className + ".txt");
        
        if (File.Exists(luaFilePath))
        {
            try
            {
                string existingCode = File.ReadAllText(luaFilePath);
                
                // 查找并替换InitComponents函数
                string updatedCode = ReplaceInitComponentsFunction(existingCode, code);
                
                File.WriteAllText(luaFilePath, updatedCode, Encoding.UTF8);
                AssetDatabase.Refresh();
                
                ShowNotification(new GUIContent("Lua文件已更新"));
                Debug.Log($"已更新Lua文件: {luaFilePath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"更新Lua文件失败: {e.Message}");
                ShowNotification(new GUIContent("更新失败"));
            }
        }
        else
        {
            ShowNotification(new GUIContent("Lua文件不存在"));
        }
    }
    
    private string ReplaceInitComponentsFunction(string originalCode, string newFunctionCode)
    {
        // 简单的函数替换逻辑
        int startIndex = originalCode.IndexOf("function " + className + ":InitComponents()");
        if (startIndex == -1)
        {
            // 如果没找到函数，在OnCreate函数后添加
            int onCreateEnd = originalCode.IndexOf("end", originalCode.IndexOf("function " + className + ":OnCreate"));
            if (onCreateEnd != -1)
            {
                return originalCode.Insert(onCreateEnd + 3, "\n\n" + newFunctionCode);
            }
            return originalCode + "\n\n" + newFunctionCode;
        }
        
        int endIndex = originalCode.IndexOf("end", startIndex);
        if (endIndex != -1)
        {
            endIndex += 3; // 包含"end"
            string before = originalCode.Substring(0, startIndex);
            string after = originalCode.Substring(endIndex);
            return before + newFunctionCode.Trim() + after;
        }
        
        return originalCode;
    }
}
